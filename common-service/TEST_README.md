# MessageCreateWithConfidentialTalent 测试说明

## 概述

本测试套件用于验证 `MessageCreateWithConfidentialTalent` 类的内容生成功能是否符合 `msg.json` 中定义的 Lark 卡片消息格式。

## 测试文件说明

### 1. MessageCreateWithConfidentialTalentTest.java
- **位置**: `common/src/test/java/com/altomni/apn/common/dto/message/MessageCreateWithConfidentialTalentTest.java`
- **类型**: JUnit 5 单元测试
- **功能**: 完整的单元测试套件，包含多种测试场景

### 2. TestRunner.java
- **位置**: 项目根目录
- **类型**: 独立的 Java 测试程序
- **功能**: 简单的测试运行器，可以直接运行验证功能

### 3. MsgJsonValidationTest.java
- **位置**: 项目根目录
- **类型**: 专门的验证程序
- **功能**: 严格按照 msg.json 的内容进行验证

## 测试数据

所有测试都基于 `msg.json` 文件中的示例数据：

```json
{
  "候选人ID": 2503524,
  "候选人姓名": "张三丰",
  "保密操作者": "Eddie Xu",
  "地址": "北京",
  "薪资": "800,000RMB/年",
  "工作经历": {
    "公司": "菲亚特克莱斯勒亚太投资有限公司",
    "职位": "亚太财务分析经理",
    "开始时间": "2019-07",
    "状态": "至今"
  },
  "教育经历": {
    "学校": "对外经济贸易学院",
    "学历": "本科",
    "专业": "金融学",
    "毕业时间": "2010"
  }
}
```

## 验证内容

### 中文内容验证 (基于 msg.json zh_cn 部分)

1. **主要描述文本** (line 153):
   ```
   候选人 [张三丰](https://your-link.com) #2503524（公司：菲亚特，职位：亚太财务分析经理）已被 Eddie Xu 设为保密
   ```

2. **候选人基本信息** (line 189):
   ```
   张三丰#2503524
   本科 | 北京 | 800,000RMB/年
   ```

3. **工作经历** (line 209):
   ```
   💼 菲亚特克莱斯勒亚太投资有限公司 · 亚太财务分析经理
   ```

4. **工作时间** (line 223):
   ```
   2019-07 - 至今 (6年)
   ```

5. **教育经历** (line 243):
   ```
   🎓 对外经济贸易学院 · 本科 · 金融学
   ```

6. **毕业时间** (line 257):
   ```
   2010 毕业
   ```

### 英文内容验证 (基于 msg.json en_us 部分)

1. **主要描述文本** (line 23):
   ```
   The candidate [Zhang Sanfeng](https://your-link.com) #2503524 (Company: Fiat, Job Title: APAC Financial Analyst Manager) was set confidential by Eddie Xu
   ```

2. **候选人基本信息** (line 58):
   ```
   Zhang Sanfeng#2503524
   Bachelor | Beijing | 800,000RMB/Year
   ```

3. **工作经历** (line 78):
   ```
   💼 Fiat Chrysler Asia Pacific Investment Co., Ltd · APAC FP&A Manager
   ```

4. **工作时间** (line 92):
   ```
   2019-07 - Present (6 years)
   ```

5. **教育经历** (line 112):
   ```
   🎓 Shanghai Institute of Foreign Trade · Bachelor · Finance
   ```

6. **毕业时间** (line 126):
   ```
   Graduated in 2010
   ```

## 运行测试

### 方法1: 运行 JUnit 测试
```bash
mvn test -Dtest=MessageCreateWithConfidentialTalentTest
```

### 方法2: 运行独立测试程序
```bash
# 编译并运行 TestRunner
javac TestRunner.java
java TestRunner

# 编译并运行 MsgJsonValidationTest
javac MsgJsonValidationTest.java
java MsgJsonValidationTest
```

## 测试覆盖范围

- ✅ 基本内容生成功能
- ✅ 中英文内容格式验证
- ✅ 与 msg.json 内容的一致性验证
- ✅ 边界情况处理（空数据、缺失字段）
- ✅ 薪资格式化（不同薪资类型）
- ✅ 学历等级优先级
- ✅ 时间格式化（工作经历、教育经历）
- ✅ 数据验证和错误处理

## 预期结果

所有测试应该通过，生成的内容应该与 msg.json 中定义的格式完全一致。如果测试失败，会显示具体的差异信息以便调试。
