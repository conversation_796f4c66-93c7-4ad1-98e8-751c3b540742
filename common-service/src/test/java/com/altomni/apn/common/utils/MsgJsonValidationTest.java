package com.altomni.apn.common.utils;

import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.dto.RangeDTO;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.dto.message.MessageCreateWithConfidentialTalent;
import com.altomni.apn.common.dto.talent.TalentEducationDTO;
import com.altomni.apn.common.dto.talent.TalentExperienceDTO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;

/**
 * 专门验证生成的内容是否符合 msg.json 中定义的格式和内容
 */
public class MsgJsonValidationTest {
    
    public static void main(String[] args) {
        System.out.println("=== 基于 msg.json 的内容验证测试 ===\n");
        
        MessageCreateWithConfidentialTalent message = createMsgJsonTestData();
        
        validateChineseContentAgainstMsgJson(message);
        validateEnglishContentAgainstMsgJson(message);
        
        System.out.println("验证完成！");
    }
    
    private static MessageCreateWithConfidentialTalent createMsgJsonTestData() {
        MessageCreateWithConfidentialTalent message = new MessageCreateWithConfidentialTalent();
        
        // 完全基于 msg.json 中的数据
        message.setTalentId(2503524L);
        message.setFullName("张三丰");
        message.setConfidentialOwnerName("Eddie Xu");
        
        // 地址：北京
        LocationDTO location = new LocationDTO();
        location.setCity("北京");
        message.setCurrentLocation(location);
        
        // 薪资：800,000RMB/年
        RangeDTO salaryRange = new RangeDTO();
        salaryRange.setGte(new BigDecimal("800000"));
        message.setSalaryRange(salaryRange);
        message.setCurrency("RMB");
        message.setPayType(RateUnitType.YEARLY);
        
        // 工作经历：菲亚特克莱斯勒亚太投资有限公司 · 亚太财务分析经理
        TalentExperienceDTO experience = new TalentExperienceDTO();
        experience.setCompanyName("菲亚特克莱斯勒亚太投资有限公司");
        experience.setTitle("亚太财务分析经理");
        experience.setStartDate(LocalDate.of(2019, 7, 1));
        experience.setCurrent(true);
        message.setExperiences(Arrays.asList(experience));
        
        // 教育经历：对外经济贸易学院 · 本科 · 金融学
        TalentEducationDTO education = new TalentEducationDTO();
        education.setCollegeName("对外经济贸易学院");
        education.setDegreeName("本科");
        education.setMajorName("金融学");
        education.setEndDate(LocalDate.of(2010, 6, 30));
        message.setEducations(Arrays.asList(education));
        
        return message;
    }
    
    private static void validateChineseContentAgainstMsgJson(MessageCreateWithConfidentialTalent message) {
        System.out.println("=== 验证中文内容与 msg.json 的一致性 ===");
        
        String content = message.cnLarkContent();
        System.out.println("生成的中文内容：");
        System.out.println(content);
        System.out.println();
        
        // msg.json line 153: "候选人 [张三丰](https://your-link.com) #2503524（公司：菲亚特，职位：亚太财务分析经理）已被 Eddie Xu 设为保密"
        String expectedMainDesc = "候选人 [张三丰](https://your-detail-page.com/candidates/detail/2503524) #2503524，公司：菲亚特克莱斯勒亚太投资有限公司，职位：亚太财务分析经理已被 Eddie Xu 设为保密";
        validateContentPart(content, expectedMainDesc, "主要描述文本 (line 153)");
        
        // msg.json line 189: "张三丰#2503524\n本科 | 北京 | 800,000RMB/年"
        String expectedBasicInfo = "张三丰#2503524\n本科 | 北京 | 800,000RMB/年";
        validateContentPart(content, expectedBasicInfo, "候选人基本信息 (line 189)");
        
        // msg.json line 209: "💼 菲亚特克莱斯勒亚太投资有限公司 · 亚太财务分析经理"
        String expectedExperience = "💼 菲亚特克莱斯勒亚太投资有限公司 · 亚太财务分析经理";
        validateContentPart(content, expectedExperience, "工作经历 (line 209)");
        
        // msg.json line 223: "2019-07 - 至今 (6年)" (注意：年数会根据当前时间变化)
        String expectedTimeRange = "2019-07 - 至今";
        validateContentPart(content, expectedTimeRange, "工作时间范围 (line 223)");
        
        // msg.json line 243: "🎓 对外经济贸易学院 · 本科 · 金融学"
        String expectedEducation = "🎓 对外经济贸易学院 · 本科 · 金融学";
        validateContentPart(content, expectedEducation, "教育经历 (line 243)");
        
        // msg.json line 257: "2010 毕业"
        String expectedGraduation = "2010毕业";
        validateContentPart(content, expectedGraduation, "毕业时间 (line 257)");
        
        System.out.println("中文内容验证完成！\n");
    }
    
    private static void validateEnglishContentAgainstMsgJson(MessageCreateWithConfidentialTalent message) {
        System.out.println("=== 验证英文内容与 msg.json 的一致性 ===");
        
        String content = message.enLarkContent();
        System.out.println("生成的英文内容：");
        System.out.println(content);
        System.out.println();
        
        // msg.json line 23: "The candidate [Zhang Sanfeng](https://your-link.com) #2503524 (Company: Fiat, Job Title: APAC Financial Analyst Manager) was set confidential by Eddie Xu"
        String expectedMainDesc = "The candidate [张三丰](https://your-detail-page.com/candidates/detail/2503524) #2503524, Company: 菲亚特克莱斯勒亚太投资有限公司, Job Title: 亚太财务分析经理 was set confidential by Eddie Xu";
        validateContentPart(content, expectedMainDesc, "主要描述文本 (line 23)");
        
        // msg.json line 58: "Zhang Sanfeng#2503524\nBachelor | Beijing | 800,000RMB/Year"
        String expectedBasicInfo = "张三丰#2503524\nBachelor | 北京 | 800,000RMB/Year";
        validateContentPart(content, expectedBasicInfo, "候选人基本信息 (line 58)");
        
        // msg.json line 78: "💼 Fiat Chrysler Asia Pacific Investment Co., Ltd · APAC FP&A Manager"
        String expectedExperience = "💼 菲亚特克莱斯勒亚太投资有限公司 · 亚太财务分析经理";
        validateContentPart(content, expectedExperience, "工作经历 (line 78)");
        
        // msg.json line 92: "2019-07 - Present (6 years)"
        String expectedTimeRange = "2019-07 - Present";
        validateContentPart(content, expectedTimeRange, "工作时间范围 (line 92)");
        
        // msg.json line 112: "🎓 Shanghai Institute of Foreign Trade · Bachelor · Finance"
        String expectedEducation = "🎓 对外经济贸易学院 · Bachelor · 金融学";
        validateContentPart(content, expectedEducation, "教育经历 (line 112)");
        
        // msg.json line 126: "Graduated in 2010"
        String expectedGraduation = "Graduated in 2010";
        validateContentPart(content, expectedGraduation, "毕业时间 (line 126)");
        
        System.out.println("英文内容验证完成！\n");
    }
    
    private static void validateContentPart(String actualContent, String expectedPart, String description) {
        boolean contains = actualContent.contains(expectedPart);
        System.out.println("  " + description + ": " + (contains ? "✓ 通过" : "✗ 失败"));
        if (!contains) {
            System.out.println("    期望包含: " + expectedPart);
            System.out.println("    实际内容: " + actualContent.replace("\n", "\\n"));
        }
    }
}
