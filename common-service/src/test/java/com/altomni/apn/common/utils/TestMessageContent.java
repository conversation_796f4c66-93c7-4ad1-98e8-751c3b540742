package com.altomni.apn.common.utils;// 简单测试文件来验证我们的实现逻辑
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.dto.RangeDTO;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.dto.message.MessageCreateWithConfidentialTalent;
import com.altomni.apn.common.dto.talent.TalentEducationDTO;
import com.altomni.apn.common.dto.talent.TalentExperienceDTO;

import java.time.LocalDate;
import java.math.BigDecimal;
import java.util.Arrays;

public class TestMessageContent {
    
    public static void main(String[] args) {
        // 创建测试数据
        MessageCreateWithConfidentialTalent message = new MessageCreateWithConfidentialTalent();
        
        // 设置基本信息
        message.setTalentId(2503524L);
        message.setFullName("张三丰");
        message.setConfidentialOwnerName("<PERSON>");
        
        // 设置地址信息
        LocationDTO location = new LocationDTO();
        location.setCity("北京");
        message.setCurrentLocation(location);
        
        // 设置薪资信息
        RangeDTO salaryRange = new RangeDTO();
        salaryRange.setGte(new BigDecimal("800000"));
        message.setSalaryRange(salaryRange);
        message.setCurrency("RMB");
        message.setPayType(RateUnitType.YEARLY);
        
        // 设置工作经历
        TalentExperienceDTO experience = new TalentExperienceDTO();
        experience.setCompanyName("菲亚特克莱斯勒亚太投资有限公司");
        experience.setTitle("亚太财务分析经理");
        experience.setStartDate(LocalDate.of(2019, 7, 1));
        experience.setCurrent(true);
        message.setExperiences(Arrays.asList(experience));
        
        // 设置教育经历
        TalentEducationDTO education = new TalentEducationDTO();
        education.setCollegeName("对外经济贸易学院");
        education.setDegreeName("本科");
        education.setMajorName("金融学");
        education.setEndDate(LocalDate.of(2010, 6, 30));
        message.setEducations(Arrays.asList(education));
        
        // 测试中文内容生成
        System.out.println("=== 中文内容 ===");
        System.out.println(message.cnLarkContent());
        
        System.out.println("\n=== 英文内容 ===");
        System.out.println(message.enLarkContent());
    }
}
