package com.altomni.apn.common.utils;

import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.dto.RangeDTO;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.dto.message.MessageCreateWithConfidentialTalent;
import com.altomni.apn.common.dto.talent.TalentEducationDTO;
import com.altomni.apn.common.dto.talent.TalentExperienceDTO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;

/**
 * 简单的测试运行器，用于验证 MessageCreateWithConfidentialTalent 的实现
 * 基于 msg.json 的内容进行验证
 */
public class TestRunner {
    
    public static void main(String[] args) {
        System.out.println("开始测试 MessageCreateWithConfidentialTalent 的内容生成功能...\n");
        
        // 创建测试数据 - 基于 msg.json 中的示例数据
        MessageCreateWithConfidentialTalent message = createTestMessage();
        
        // 测试中文内容生成
        testChineseContent(message);
        
        // 测试英文内容生成
        testEnglishContent(message);
        
        // 测试边界情况
        testEdgeCases();
        
        System.out.println("所有测试完成！");
    }
    
    private static MessageCreateWithConfidentialTalent createTestMessage() {
        MessageCreateWithConfidentialTalent message = new MessageCreateWithConfidentialTalent();
        
        // 基本信息 - 基于 msg.json
        message.setTalentId(2503524L);
        message.setFullName("张三丰");
        message.setConfidentialOwnerName("Eddie Xu");
        
        // 地址信息
        LocationDTO location = new LocationDTO();
        location.setCity("北京");
        message.setCurrentLocation(location);
        
        // 薪资信息
        RangeDTO salaryRange = new RangeDTO();
        salaryRange.setGte(new BigDecimal("800000"));
        message.setSalaryRange(salaryRange);
        message.setCurrency("RMB");
        message.setPayType(RateUnitType.YEARLY);
        
        // 工作经历
        TalentExperienceDTO experience = new TalentExperienceDTO();
        experience.setCompanyName("菲亚特克莱斯勒亚太投资有限公司");
        experience.setTitle("亚太财务分析经理");
        experience.setStartDate(LocalDate.of(2019, 7, 1));
        experience.setCurrent(true);
        message.setExperiences(Arrays.asList(experience));
        
        // 教育经历
        TalentEducationDTO education = new TalentEducationDTO();
        education.setCollegeName("对外经济贸易学院");
        education.setDegreeName("本科");
        education.setMajorName("金融学");
        education.setEndDate(LocalDate.of(2010, 6, 30));
        message.setEducations(Arrays.asList(education));
        
        return message;
    }
    
    private static void testChineseContent(MessageCreateWithConfidentialTalent message) {
        System.out.println("=== 测试中文内容生成 ===");
        String cnContent = message.cnLarkContent();
        System.out.println("生成的中文内容：");
        System.out.println(cnContent);
        System.out.println();
        
        // 验证关键内容
        System.out.println("验证中文内容：");
        checkContent(cnContent, "候选人 [张三丰]", "候选人姓名链接");
        checkContent(cnContent, "#2503524", "候选人ID");
        checkContent(cnContent, "公司：菲亚特克莱斯勒亚太投资有限公司", "公司信息");
        checkContent(cnContent, "职位：亚太财务分析经理", "职位信息");
        checkContent(cnContent, "已被 Eddie Xu 设为保密", "保密操作者");
        checkContent(cnContent, "张三丰#2503524", "基本信息格式");
        checkContent(cnContent, "本科 | 北京 | 800,000RMB/年", "学历地址薪资");
        checkContent(cnContent, "💼 菲亚特克莱斯勒亚太投资有限公司 · 亚太财务分析经理", "工作经历");
        checkContent(cnContent, "2019-07 - 至今", "工作时间");
        checkContent(cnContent, "🎓 对外经济贸易学院 · 本科 · 金融学", "教育经历");
        checkContent(cnContent, "2010毕业", "毕业时间");
        System.out.println("中文内容验证完成！\n");
    }
    
    private static void testEnglishContent(MessageCreateWithConfidentialTalent message) {
        System.out.println("=== 测试英文内容生成 ===");
        String enContent = message.enLarkContent();
        System.out.println("生成的英文内容：");
        System.out.println(enContent);
        System.out.println();
        
        // 验证关键内容
        System.out.println("验证英文内容：");
        checkContent(enContent, "The candidate [张三丰]", "候选人姓名链接");
        checkContent(enContent, "#2503524", "候选人ID");
        checkContent(enContent, "Company: 菲亚特克莱斯勒亚太投资有限公司", "公司信息");
        checkContent(enContent, "Job Title: 亚太财务分析经理", "职位信息");
        checkContent(enContent, "was set confidential by Eddie Xu", "保密操作者");
        checkContent(enContent, "张三丰#2503524", "基本信息格式");
        checkContent(enContent, "Bachelor | 北京 | 800,000RMB/Year", "学历地址薪资");
        checkContent(enContent, "💼 菲亚特克莱斯勒亚太投资有限公司 · 亚太财务分析经理", "工作经历");
        checkContent(enContent, "2019-07 - Present", "工作时间");
        checkContent(enContent, "🎓 对外经济贸易学院 · Bachelor · 金融学", "教育经历");
        checkContent(enContent, "Graduated in 2010", "毕业时间");
        System.out.println("英文内容验证完成！\n");
    }
    
    private static void testEdgeCases() {
        System.out.println("=== 测试边界情况 ===");
        
        // 测试空数据
        MessageCreateWithConfidentialTalent emptyMessage = new MessageCreateWithConfidentialTalent();
        emptyMessage.setTalentId(123L);
        emptyMessage.setFullName("Test User");
        
        String cnEmpty = emptyMessage.cnLarkContent();
        String enEmpty = emptyMessage.enLarkContent();
        
        System.out.println("空数据测试 - 中文：" + (cnEmpty != null && cnEmpty.contains("Test User#123") ? "通过" : "失败"));
        System.out.println("空数据测试 - 英文：" + (enEmpty != null && enEmpty.contains("Test User#123") ? "通过" : "失败"));
        
        System.out.println("边界情况测试完成！\n");
    }
    
    private static void checkContent(String content, String expected, String description) {
        boolean contains = content.contains(expected);
        System.out.println("  " + description + ": " + (contains ? "✓ 通过" : "✗ 失败 - 未找到: " + expected));
    }
}
