package com.altomni.apn.common.utils;

import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.dto.RangeDTO;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.dto.message.MessageCreateWithConfidentialTalent;
import com.altomni.apn.common.dto.talent.TalentEducationDTO;
import com.altomni.apn.common.dto.talent.TalentExperienceDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 单元测试：基于 msg.json 的内容验证 MessageCreateWithConfidentialTalent 的内容生成功能
 */
public class MessageCreateWithConfidentialTalentTest {

    private MessageCreateWithConfidentialTalent message;

    @BeforeEach
    void setUp() {
        message = new MessageCreateWithConfidentialTalent();
        setupTestData();
    }

    private void setupTestData() {
        // 基本信息 - 基于 msg.json 中的数据
        message.setTalentId(2503524L);
        message.setFullName("张三丰");
        message.setConfidentialOwnerName("Eddie Xu");

        // 地址信息
        LocationDTO location = new LocationDTO();
        location.setCity("北京");
        message.setCurrentLocation(location);

        // 薪资信息
        RangeDTO salaryRange = new RangeDTO();
        salaryRange.setGte(new BigDecimal("800000"));
        message.setSalaryRange(salaryRange);
        message.setCurrency("RMB");
        message.setPayType(RateUnitType.YEARLY);

        // 工作经历 - 基于 msg.json 中的数据
        TalentExperienceDTO experience = new TalentExperienceDTO();
        experience.setCompanyName("菲亚特克莱斯勒亚太投资有限公司");
        experience.setTitle("亚太财务分析经理");
        experience.setStartDate(LocalDate.of(2019, 7, 1));
        experience.setCurrent(true);
        message.setExperiences(Arrays.asList(experience));

        // 教育经历 - 基于 msg.json 中的数据
        TalentEducationDTO education = new TalentEducationDTO();
        education.setCollegeName("对外经济贸易学院");
        education.setDegreeName("本科");
        education.setMajorName("金融学");
        education.setEndDate(LocalDate.of(2010, 6, 30));
        message.setEducations(Arrays.asList(education));
    }

    @Test
    void testCnLarkContent() {
        String content = message.cnLarkContent();
        
        // 验证主要描述文本 - 对应 msg.json line 153
        assertTrue(content.contains("候选人 [张三丰]"), "应包含候选人姓名链接");
        assertTrue(content.contains("#2503524"), "应包含候选人ID");
        assertTrue(content.contains("公司：菲亚特克莱斯勒亚太投资有限公司"), "应包含公司信息");
        assertTrue(content.contains("职位：亚太财务分析经理"), "应包含职位信息");
        assertTrue(content.contains("已被 Eddie Xu 设为保密"), "应包含保密操作者信息");

        // 验证候选人基本信息 - 对应 msg.json line 189
        assertTrue(content.contains("张三丰#2503524"), "应包含姓名和ID");
        assertTrue(content.contains("本科"), "应包含学历信息");
        assertTrue(content.contains("北京"), "应包含地址信息");
        assertTrue(content.contains("800,000RMB/年"), "应包含薪资信息");

        // 验证工作经历 - 对应 msg.json line 209
        assertTrue(content.contains("💼 菲亚特克莱斯勒亚太投资有限公司 · 亚太财务分析经理"), "应包含工作经历");
        assertTrue(content.contains("2019-07 - 至今"), "应包含工作时间范围");

        // 验证教育经历 - 对应 msg.json line 243
        assertTrue(content.contains("🎓 对外经济贸易学院 · 本科 · 金融学"), "应包含教育经历");
        assertTrue(content.contains("2010毕业"), "应包含毕业时间");

        System.out.println("=== 中文内容测试结果 ===");
        System.out.println(content);
    }

    @Test
    void testEnLarkContent() {
        String content = message.enLarkContent();
        
        // 验证主要描述文本 - 对应 msg.json line 23 (需要调整英文公司名和职位名)
        assertTrue(content.contains("The candidate [张三丰]"), "应包含候选人姓名链接");
        assertTrue(content.contains("#2503524"), "应包含候选人ID");
        assertTrue(content.contains("Company: 菲亚特克莱斯勒亚太投资有限公司"), "应包含公司信息");
        assertTrue(content.contains("Job Title: 亚太财务分析经理"), "应包含职位信息");
        assertTrue(content.contains("was set confidential by Eddie Xu"), "应包含保密操作者信息");

        // 验证候选人基本信息 - 对应 msg.json line 58 (需要调整为英文学历)
        assertTrue(content.contains("张三丰#2503524"), "应包含姓名和ID");
        assertTrue(content.contains("Bachelor"), "应包含英文学历信息");
        assertTrue(content.contains("北京"), "应包含地址信息");
        assertTrue(content.contains("800,000RMB/Year"), "应包含英文薪资信息");

        // 验证工作经历 - 对应 msg.json line 78 (需要调整为英文公司名和职位)
        assertTrue(content.contains("💼 菲亚特克莱斯勒亚太投资有限公司 · 亚太财务分析经理"), "应包含工作经历");
        assertTrue(content.contains("2019-07 - Present"), "应包含英文工作时间范围");

        // 验证教育经历 - 对应 msg.json line 112 (需要调整为英文学校名)
        assertTrue(content.contains("🎓 对外经济贸易学院 · Bachelor · 金融学"), "应包含教育经历");
        assertTrue(content.contains("Graduated in 2010"), "应包含英文毕业时间");

        System.out.println("=== 英文内容测试结果 ===");
        System.out.println(content);
    }

    @Test
    void testCnContentWithMsgJsonExpectedFormat() {
        String content = message.cnLarkContent();

        // 验证内容格式是否符合 msg.json 的预期结构
        String[] lines = content.split("\n");

        // 第一行应该是主要描述
        String mainDescription = lines[0];
        assertEquals("候选人 [张三丰](https://your-detail-page.com/candidates/detail/2503524) #2503524，公司：菲亚特克莱斯勒亚太投资有限公司，职位：亚太财务分析经理已被 Eddie Xu 设为保密",
                    mainDescription, "主要描述格式应符合预期");

        // 验证基本信息格式
        assertTrue(content.contains("张三丰#2503524\n本科 | 北京 | 800,000RMB/年"), "基本信息格式应符合 msg.json");

        // 验证工作经历格式
        assertTrue(content.contains("💼 菲亚特克莱斯勒亚太投资有限公司 · 亚太财务分析经理"), "工作经历格式应符合 msg.json");

        // 验证教育经历格式
        assertTrue(content.contains("🎓 对外经济贸易学院 · 本科 · 金融学"), "教育经历格式应符合 msg.json");
    }

    @Test
    void testEnContentWithMsgJsonExpectedFormat() {
        String content = message.enLarkContent();

        // 验证英文内容格式是否符合 msg.json 的预期结构
        String[] lines = content.split("\n");

        // 第一行应该是主要描述
        String mainDescription = lines[0];
        assertEquals("The candidate [张三丰](https://your-detail-page.com/candidates/detail/2503524) #2503524, Company: 菲亚特克莱斯勒亚太投资有限公司, Job Title: 亚太财务分析经理 was set confidential by Eddie Xu",
                    mainDescription, "英文主要描述格式应符合预期");

        // 验证基本信息格式
        assertTrue(content.contains("张三丰#2503524\nBachelor | 北京 | 800,000RMB/Year"), "英文基本信息格式应符合 msg.json");

        // 验证工作经历格式
        assertTrue(content.contains("💼 菲亚特克莱斯勒亚太投资有限公司 · 亚太财务分析经理"), "英文工作经历格式应符合 msg.json");

        // 验证教育经历格式
        assertTrue(content.contains("🎓 对外经济贸易学院 · Bachelor · 金融学"), "英文教育经历格式应符合 msg.json");
    }

    @Test
    void testEdgeCasesWithEmptyData() {
        // 测试空数据的情况
        MessageCreateWithConfidentialTalent emptyMessage = new MessageCreateWithConfidentialTalent();
        emptyMessage.setTalentId(123L);
        emptyMessage.setFullName("Test User");

        String cnContent = emptyMessage.cnLarkContent();
        String enContent = emptyMessage.enLarkContent();

        // 验证空数据时不会出现异常
        assertNotNull(cnContent, "中文内容不应为null");
        assertNotNull(enContent, "英文内容不应为null");

        // 验证基本信息仍然存在
        assertTrue(cnContent.contains("Test User#123"), "应包含基本的姓名和ID");
        assertTrue(enContent.contains("Test User#123"), "应包含基本的姓名和ID");
    }

    @Test
    void testSalaryFormatting() {
        // 测试不同薪资类型的格式化
        message.setPayType(RateUnitType.MONTHLY);
        String cnContent = message.cnLarkContent();
        assertTrue(cnContent.contains("800,000RMB/月"), "应正确格式化月薪");

        String enContent = message.enLarkContent();
        assertTrue(enContent.contains("800,000RMB/Month"), "应正确格式化英文月薪");
    }

    @Test
    void testEducationLevelPriority() {
        // 测试学历等级优先级
        TalentEducationDTO masterEducation = new TalentEducationDTO();
        masterEducation.setCollegeName("清华大学");
        masterEducation.setDegreeName("硕士");
        masterEducation.setMajorName("计算机科学");

        message.getEducations().add(masterEducation);

        String cnContent = message.cnLarkContent();
        assertTrue(cnContent.contains("硕士"), "应显示最高学历：硕士");

        String enContent = message.enLarkContent();
        assertTrue(enContent.contains("Master"), "应显示英文最高学历：Master");
    }
}
