package com.altomni.apn.common.service.message.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.enumeration.message.MessageDeleteEnum;
import com.altomni.apn.common.domain.enumeration.message.MessageFavoriteEnum;
import com.altomni.apn.common.domain.enumeration.message.MessageStatusEnum;
import com.altomni.apn.common.domain.enumeration.message.MessageTypeEnum;
import com.altomni.apn.common.domain.enumeration.xxljob.XxlJobRelationTypeEnum;
import com.altomni.apn.common.domain.message.Message;
import com.altomni.apn.common.domain.message.MessageUserRelation;
import com.altomni.apn.common.dto.message.*;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.dto.xxljob.XxlJobApnDTO;
import com.altomni.apn.common.dto.xxljob.XxlJobApnParamDTO;
import com.altomni.apn.common.enumeration.enums.NoPoachingRemindType;
import com.altomni.apn.common.repository.message.MessageCustomRepository;
import com.altomni.apn.common.repository.message.MessageRepository;
import com.altomni.apn.common.repository.message.MessageUserRelationRepository;
import com.altomni.apn.common.service.enums.EnumCurrencyService;
import com.altomni.apn.common.service.enums.EnumDegreeService;
import com.altomni.apn.common.service.lark.LarkClient;
import com.altomni.apn.common.service.message.MessageService;
import com.altomni.apn.common.service.sse.SseClient;
import com.altomni.apn.common.service.user.UserService;
import com.altomni.apn.common.service.xxljob.XxlJobService;
import com.altomni.apn.common.utils.MessageUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.message.MessageVO;
import com.altomni.apn.common.vo.user.UserTimeZoneVO;
import com.amazonaws.util.IOUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.*;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.net.URL;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service("messageService")
public class MessageServiceImpl implements MessageService {

    private static final String VOICEMAIL_CN_TITLE = "候选人语音留言";

    private static final String VOICEMAIL_EN_TITLE = "Candidate Voice Message";

    private static final String NO_POACHING_TALENT_CN_TITLE = "禁猎候选人提醒";

    private static final String NO_POACHING_TALENT_EN_TITLE = "No poaching candidate notification";

    private static final String NO_POACHING_CONTACT_CN_TITLE = "客户联系人被推荐提醒";

    private static final String NO_POACHING_CONTACT_EN_TITLE = "Client's contact was poached notification";

    private static final String CONFIDENTIAL_TALENT_CN_TITLE = "保密候选人";

    private static final String CONFIDENTIAL_TALENT_EN_TITLE = "Confidential Candidates";

    @Value("${application.mainPath.baseUrl}")
    private String baseUrl;


    @Resource
    private MessageCustomRepository messageCustomRepository;

    @Resource
    private UserService userService;

    @Resource
    private MessageRepository messageRepository;

    @Resource
    private XxlJobService xxlJobService;

    @Resource
    private SseClient sseClient;

    @Resource
    private MessageUserRelationRepository messageUserRelationRepository;

    @Resource
    private LarkClient larkClient;

    @Resource
    private EnumDegreeService enumDegreeService;

    @Resource
    private EnumCurrencyService enumCurrencyService;

    /**
     * 消息的统计接口
     * @return
     */
    @Override
    public Map<String, Integer> messageStatistics() {
        return messageCustomRepository.messageStatistics();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createMessage(MessageCreateDTO messageCreateDTO) {
        log.info("create message type = {}", messageCreateDTO.getType());
        Message message = new Message();
        BeanUtil.copyProperties(messageCreateDTO, message);
        message.setCnTitle(messageCreateDTO.getTitle());
        message.setEnTitle(messageCreateDTO.getTitle());
        message.setTenantId(SecurityUtils.getTenantId());
        JSONObject contextJson = MessageUtil.setContextJsonWithPrimitiveMessage(XxlJobRelationTypeEnum.ADMIN_MESSAGE, message, 1);
        message.setContent(JSONUtil.toJsonStr(contextJson));
        messageRepository.save(message);
        if (!Objects.equals(MessageTypeEnum.ADMIN_MESSAGE, messageCreateDTO.getType())) {
            MessageUserRelation messageUserRelation = new MessageUserRelation();
            messageUserRelation.setUserId(SecurityUtils.getUserId());
            messageUserRelation.setMessageId(message.getId());
            messageUserRelationRepository.save(messageUserRelation);
        }
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            try {
                if (Objects.equals(MessageTypeEnum.ADMIN_MESSAGE, messageCreateDTO.getType())) {
                    List<UserBriefDTO> userBriefDTOList = userService.getAllBriefUsers().getBody();
                    if (CollUtil.isNotEmpty(userBriefDTOList)) {
                        log.info("create message userIdSize = {}", userBriefDTOList.size());
                        List<Long> userIdSet = userBriefDTOList.stream().filter(UserBriefDTO::isActivated).map(UserBriefDTO::getId).distinct().toList();
                        List<UserTimeZoneVO> userTimeZoneVOList = userService.getTimezoneListByUserIdList(userIdSet).getBody();
                        if (CollUtil.isNotEmpty(userTimeZoneVOList)) {
                            List<XxlJobApnDTO> xxlJobApnDTOList = new ArrayList<>();
                            userTimeZoneVOList.forEach(userTimeZoneVO -> {
                                //add xxl-job
                                if (StrUtil.isBlank(userTimeZoneVO.getCustomTimezone())) {
                                    return;
                                }
                                LocalDateTime sendTime = messageCreateDTO.getSendTime();
                                Instant triggerTime = sendTime.atZone(ZoneId.of(userTimeZoneVO.getCustomTimezone())).toInstant();
                                XxlJobApnDTO xxlJobApnDTO = new XxlJobApnDTO();
                                xxlJobApnDTO.setJobDesc("系统管理员消息#userId=" + userTimeZoneVO.getUserId() + "#title=" + message.getCnTitle());
                                XxlJobApnParamDTO xxlJobApnParamDTO = new XxlJobApnParamDTO();
                                xxlJobApnParamDTO.setXxlJobType(XxlJobRelationTypeEnum.ADMIN_MESSAGE);
                                xxlJobApnParamDTO.setReferenceId(message.getId());
                                xxlJobApnParamDTO.setUserId(userTimeZoneVO.getUserId());
                                xxlJobApnParamDTO.setTenantId(SecurityUtils.getTenantId());
                                xxlJobApnParamDTO.setTimezone(userTimeZoneVO.getCustomTimezone());
                                xxlJobApnParamDTO.setSendTime(triggerTime);
                                xxlJobApnParamDTO.setToken(SecurityUtils.getCurrentUserToken());
                                xxlJobApnDTO.setXxlJobApnParamDTO(xxlJobApnParamDTO);
                                Map<String, Object> paramMap = new JSONObject(xxlJobApnParamDTO);
                                paramMap.put("messageId", message.getId());
                                xxlJobApnParamDTO.setXxlJobParam(paramMap);
                                xxlJobApnDTOList.add(xxlJobApnDTO);
                                log.info("create message messageId = {}, userId = {}, sendTime = {}", message.getId(), userTimeZoneVO.getUserId(), triggerTime);
                            });
                            log.info("create admin message messageId = {}", message.getId());
                            xxlJobService.createXxlJobs(xxlJobApnDTOList);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("create message xxl job is error, messageId = {}, msg = {}", message.getId(), ExceptionUtils.getStackTrace(e));
            }
        });
    }

    @Override
    public Page<MessageVO> searchMessagePage(MessageSearchPageDTO messageSearchPageDTO) {
        List<MessageVO> voList = messageCustomRepository.searchMessageList(messageSearchPageDTO);
        long count = messageCustomRepository.countMessage(messageSearchPageDTO);
        return new PageImpl<>(voList, Pageable.unpaged(), count);
    }

    @Override
    public void deleteMessage(MessageTypeEnum type) {
        if (MessageTypeEnum.FAVORITE == type) {
            //如果是收藏夹,则表示返回收藏的内容
            messageUserRelationRepository.deleteByUserIdAndTenantId(SecurityUtils.getUserId(), SecurityUtils.getTenantId());
        } else {
            messageUserRelationRepository.deleteByTypeAndUserIdAndTenantId(type.toDbValue(), SecurityUtils.getUserId(), SecurityUtils.getTenantId(), MessageDeleteEnum.DELETED.toDbValue());
        }
    }

    @Override
    public void updateMessageReaderStatusById(Long messageId) {
        messageUserRelationRepository.updateReaderStatusByUserIdAndMessageId(messageId, SecurityUtils.getUserId(), MessageStatusEnum.READ.toDbValue());
    }

    @Override
    public void updateMessageFavoriteStatusById(Long id, MessageFavoriteEnum favorite) {
        messageUserRelationRepository.updateFavoriteStatusByUserIdAndMessageId(id, SecurityUtils.getUserId(), favorite.toDbValue());
    }

    /**
     * 匹配刷一下理数的message数据
     */
    @Override
    public void historyHandlerMessage() {
        //开始遍历处理message
        log.info("开始 message 数据清理");
        Instant startDate = Instant.now();
        int page = 0;
        int size = 500;
        Pageable pageable;
        Sort sort = Sort.by(Sort.Direction.DESC, "id");
        List<XxlJobRelationTypeEnum> xxlJobRelationTypeEnumList = Arrays.stream(XxlJobRelationTypeEnum.values()).toList();
        // 日程和系统消息单独处理
        List<MessageTypeEnum> messageTypeList = CollUtil.newArrayList(MessageTypeEnum.ADMIN_MESSAGE, MessageTypeEnum.SYSTEM_MESSAGE);
        while (true) {
            pageable = PageRequest.of(page, size, sort);
            Page<Message> messagePage = messageRepository.findAll(pageable);
            List<Message> messageList = messagePage.getContent();
            if (CollUtil.isEmpty(messageList)) {
                log.info("message 数据清理结束");
                break;
            }
            messageList.parallelStream()
                    // 已经处理过的数据跳过
                    .filter(message -> !JSONUtil.isJson(message.getContent()))
                    .forEach(message -> {
                JSONObject contentJson;
                if (messageTypeList.contains(message.getType())) {
                    if (Objects.equals(MessageTypeEnum.ADMIN_MESSAGE, message.getType())) {
                        contentJson = MessageUtil.setContextJsonWithPrimitiveMessage(XxlJobRelationTypeEnum.ADMIN_MESSAGE, message, 1);
                    } else {
                        contentJson = MessageUtil.setContextJsonWithPrimitiveMessage(XxlJobRelationTypeEnum.CALENDAR, message, 1);
                    }
                    log.info("message id = {}, extract result = {}", message.getId(), JSONUtil.toJsonStr(contentJson));
                    messageRepository.updateMessageTitleContent(message.getId(), message.getCnTitle(), JSONUtil.toJsonStr(contentJson));
                } else {
                    List<XxlJobRelationTypeEnum> targetList = new ArrayList<>();
                    //内容需要解析的部分
                    Map<String, String> map = extractValuesAndGenerateJson(xxlJobRelationTypeEnumList, message.getCnTitle(), targetList);
                    if (CollUtil.isEmpty(map) || CollUtil.isEmpty(targetList)) {
                        log.warn("message id = {}, extract values is fail", message.getId());
                    } else {
                        XxlJobRelationTypeEnum target = targetList.get(0);
                        contentJson = MessageUtil.setContextJsonWithMap(target, map, 1);
                        log.info("message id = {}, extract result = {}", message.getId(), JSONUtil.toJsonStr(contentJson));
                        //title 由前端自己保存,后端这里设置为空
                        messageRepository.updateMessageTitleContent(message.getId(), null, JSONUtil.toJsonStr(contentJson));
                    }
                }
            });
            page++;
        }
        log.info("结束 message 数据清理, cost = {}s", Duration.between(startDate, Instant.now()).toSeconds());
    }

    @Override
    public void createMessageWithTalentInfoUpdate(MessageCreateWithTalentInfoDTO messageCreateDTO) {
        Message message = new Message();
        Map<String, String> map = new HashMap<>(16);
        map.put("talentName", messageCreateDTO.getTalentName());
        JSONObject content = MessageUtil.setContextJsonWithMap(XxlJobRelationTypeEnum.TALENT_INFO_UPDATE_WARN, map, 1);
        message.setContent(JSONUtil.toJsonStr(content));
        message.setTenantId(messageCreateDTO.getTenantId());
        message.setType(XxlJobRelationTypeEnum.TALENT_INFO_UPDATE_WARN.getMessageType());
        Optional.of(messageRepository.save(message)).ifPresent(saveMessage -> Optional.ofNullable(messageCreateDTO.getUserIdList()).ifPresent(userIdList -> {
            List<MessageUserRelation> messageUserRelationList = userIdList.stream().map(userId -> {
                MessageUserRelation messageUserRelation = new MessageUserRelation();
                messageUserRelation.setUserId(userId);
                messageUserRelation.setMessageId(saveMessage.getId());
                return messageUserRelation;
            }).toList();
            messageUserRelationRepository.saveAll(messageUserRelationList);
            messageUserRelationList.forEach(messageUserRelation -> sseClient.pushSseReminderMessageRefresh(messageUserRelation.getUserId(), messageCreateDTO.getTenantId()));
        }));
    }

    @Override
    public void createMessageWithVoicemail(MessageCreateWithVoicemailDTO messageCreateWithVoicemailDTO) {
        Message message = new Message();
        Map<String, String> map = new HashMap<>(16);
        map.put("talentName", messageCreateWithVoicemailDTO.getTalentName());
        map.put("talentId", messageCreateWithVoicemailDTO.getTalentId().toString());
        JSONObject content = MessageUtil.setContextJsonWithMap(XxlJobRelationTypeEnum.VOICEMAIL_MESSAGE, map, 1);
        message.setContent(JSONUtil.toJsonStr(content));
        message.setCnTitle(VOICEMAIL_CN_TITLE);
        message.setEnTitle(VOICEMAIL_EN_TITLE);
        message.setTenantId(messageCreateWithVoicemailDTO.getUserBriefDTO().getTenantId());
        message.setType(XxlJobRelationTypeEnum.VOICEMAIL_MESSAGE.getMessageType());
        Optional.of(messageRepository.save(message)).ifPresent(saveMessage -> Optional.ofNullable(messageCreateWithVoicemailDTO.getUserBriefDTO()).ifPresent(userBriefDTO -> {
            MessageUserRelation messageUserRelation = new MessageUserRelation();
            messageUserRelation.setUserId(userBriefDTO.getId());
            messageUserRelation.setMessageId(saveMessage.getId());
            messageUserRelationRepository.save(messageUserRelation);
            sseClient.pushSseReminderMessageRefresh(messageUserRelation.getUserId(), messageCreateWithVoicemailDTO.getTenantId());
        }));
    }

    @Override
    public void createMessageWithNoPoachingSubmit(MessageCreateWithNoPoachingSubmitDTO messageCreateDTO) {
        Message message = new Message();
        Map<String, String> map = new HashMap<>(16);
        map.put("remindType", messageCreateDTO.getRemindType().getDisplay());
        map.put("companyId", messageCreateDTO.getCompanyId().toString());
        map.put("companyName", messageCreateDTO.getCompanyName());
        map.put("talentFullName", messageCreateDTO.getTalentFullName());
        map.put("talentId", messageCreateDTO.getTalentId().toString());
        map.put("submitUserId", messageCreateDTO.getSubmitUserId().toString());
        map.put("submitUserFullName", messageCreateDTO.getSubmitUserFullName());
        map.put("jobId", messageCreateDTO.getJobId().toString());
        map.put("jobTitle", messageCreateDTO.getJobTitle());
        JSONObject content = MessageUtil.setContextJsonWithMap(XxlJobRelationTypeEnum.NO_POACHING_REMINDER, map, 1);
        message.setContent(JSONUtil.toJsonStr(content));
        if(NoPoachingRemindType.CONTACT.equals(messageCreateDTO.getRemindType())) {
            message.setCnTitle(NO_POACHING_CONTACT_CN_TITLE);
            message.setEnTitle(NO_POACHING_CONTACT_EN_TITLE);
        }else {
            message.setCnTitle(NO_POACHING_TALENT_CN_TITLE);
            message.setEnTitle(NO_POACHING_TALENT_EN_TITLE);
        }
        message.setTenantId(messageCreateDTO.getTenantId());
        message.setType(XxlJobRelationTypeEnum.NO_POACHING_REMINDER.getMessageType());

        List<MessageUserRelation> relations = new ArrayList<>();
        Optional.of(messageRepository.save(message)).ifPresent(saveMessage -> messageCreateDTO.getNeedRemindUserIds().forEach(userId -> {
            MessageUserRelation messageUserRelation = new MessageUserRelation();
            messageUserRelation.setUserId(userId);
            messageUserRelation.setMessageId(saveMessage.getId());
            relations.add(messageUserRelation);
        }));
        messageUserRelationRepository.saveAll(relations);

        relations.forEach(messageUserRelation -> sseClient.pushSseReminderMessageRefresh(messageUserRelation.getUserId(), messageCreateDTO.getTenantId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createMessageWithConfidentialTalent(MessageCreateWithConfidentialTalent messageCreateDTO) {
        List<UserBriefDTO> sendUsers = userService.getBriefUsersByIds(List.of(messageCreateDTO.getNeedRemindUserId())).getBody();
        if (CollectionUtils.isEmpty(sendUsers)) {
            log.info("createMessageWithConfidentialTalent briefUsers is empty");
            return;
        }
        Message message = new Message();
        message.setCnTitle(CONFIDENTIAL_TALENT_CN_TITLE);
        message.setEnTitle(CONFIDENTIAL_TALENT_EN_TITLE);
        message.setType(MessageTypeEnum.SYSTEM_MESSAGE);
        message.setTenantId(messageCreateDTO.getTenantId());
        Map<String, String> map = new HashMap<>(16);
        map.put("talentId", messageCreateDTO.getTalentId().toString());
        map.put("talentFullName", messageCreateDTO.getFullName());
        messageCreateDTO.nearExperience().ifPresentOrElse(experience -> {
            map.put("companyName", experience.getCompanyName());
            map.put("title", experience.getTitle());
        },() -> {
            map.put("companyName", "");
            map.put("title", "");
        });
        JSONObject content = MessageUtil.setContextJsonWithMap(XxlJobRelationTypeEnum.TALENT_DECLASSIFY, map, 1);
        String jsonContent = JSONUtil.toJsonStr(content);
        message.setContent(jsonContent);

//        Message savedMessage = messageRepository.save(message);
//
//        MessageUserRelation messageUserRelation = new MessageUserRelation();
//        messageUserRelation.setUserId(messageCreateDTO.getNeedRemindUserId());
//        messageUserRelation.setMessageId(savedMessage.getId());
//        messageUserRelationRepository.save(messageUserRelation);
//
//        // 推送站内消息提醒
//        sseClient.pushSseReminderMessageRefresh(messageCreateDTO.getNeedRemindUserId(), messageCreateDTO.getTenantId());

        // 推送 lark 卡片消息
        sendTalentConfidentialMsgToLark(messageCreateDTO, sendUsers.get(0));

    }

    private void sendTalentConfidentialMsgToLark(MessageCreateWithConfidentialTalent messageCreateDTO, UserBriefDTO userBriefDTO) {
        String email = userBriefDTO.getEmail();
//        String email = "<EMAIL>";
        Map<String, String> emailUserIdMap = larkClient.searchLarkUserIdByEmail(List.of(email));
        String larkUserId = emailUserIdMap.get(email);
        if (StrUtil.isBlank(larkUserId)) {
            log.info("search lark userId is not found from lark, email = {}", email);
            return;
        }

        Function<String, String> uploadImgGetKey = photoUrl -> {
            if (photoUrl == null) {
                return null;
            }
            try {
                byte[] bytes = IOUtils.toByteArray(new URL(photoUrl).openStream());
                String imageKey = larkClient.uploadImages(bytes);
                if (StrUtil.isBlank(imageKey)) {
                    log.info("update images to lark is error");
                    return null;
                }
                return imageKey;
            } catch (Exception e) {
                log.error("upload images to lark is error, msg = {}", ExceptionUtils.getStackTrace(e));
                return null;
            }
        };
        messageCreateDTO.fillExtraInfo(enumCurrencyService, enumDegreeService, baseUrl, uploadImgGetKey);
        JSONObject jsonMsg = messageCreateDTO.generateLarkMessageJson();
        larkClient.sendConfidentialTalentCard(larkUserId, JSONUtil.toJsonStr(jsonMsg));
    }

    public static Map<String, String> extractValuesAndGenerateJson(List<XxlJobRelationTypeEnum> xxlJobRelationTypeEnumList, String message, List<XxlJobRelationTypeEnum> targetList) {
        LinkedHashMap<String, String> result = new LinkedHashMap<>();
        int maxSize = 0;
        for (XxlJobRelationTypeEnum XxlJobRelationTypeEnum : xxlJobRelationTypeEnumList) {
            String template = XxlJobRelationTypeEnum.getCnMessage();
            if (StrUtil.isBlank(template)) {
                continue;
            }
            LinkedHashMap<String, String> resultMap = new LinkedHashMap<>();
            Pattern pattern = Pattern.compile("\\{([^}]*)\\}");
            Matcher templateMatcher = pattern.matcher(template);

            String regex = template.replace("(", "\\(").replace(")", "\\)").replace("[", "\\[").replace("]", "\\]");
            while (templateMatcher.find()) {
                String placeholder = templateMatcher.group(1);
                regex = regex.replace("{" + placeholder + "}", "(.*?)");
            }

            Pattern messagePattern = Pattern.compile(regex);
            Matcher messageMatcher = messagePattern.matcher(message);
            if (messageMatcher.find()) {
                templateMatcher.reset();
                int groupIndex = 1;
                while (templateMatcher.find()) {
                    String placeholder = templateMatcher.group(1);
                    String value = messageMatcher.group(groupIndex++);
                    resultMap.put(placeholder, value);
                }
            }
            if (CollUtil.isNotEmpty(resultMap) && resultMap.size() > maxSize) {
                maxSize = result.size();
                result = ObjectUtil.cloneByStream(resultMap);
                //添加匹配到的数据类型
                targetList.clear();
                targetList.add(XxlJobRelationTypeEnum);
                resultMap.clear();
            }
        }
        return result;
    }

}
