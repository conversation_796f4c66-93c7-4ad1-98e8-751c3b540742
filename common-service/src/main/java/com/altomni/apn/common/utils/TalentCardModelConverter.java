package com.altomni.apn.common.utils;

import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.dto.RangeDTO;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.dto.message.MessageCreateWithConfidentialTalent;
import com.altomni.apn.common.dto.talent.TalentEducationDTO;
import com.altomni.apn.common.dto.talent.TalentExperienceDTO;
import com.altomni.apn.common.utils.model.EducationModel;
import com.altomni.apn.common.utils.model.ExperienceModel;
import com.altomni.apn.common.utils.model.TalentCardModel;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Utility class for converting MessageCreateWithConfidentialTalent to TalentCardModel
 * and handling data transformation for HTML template rendering.
 */
@UtilityClass
@Slf4j
public class TalentCardModelConverter {

    private static final DecimalFormat SALARY_FORMAT = new DecimalFormat("#,###");

    /**
     * Converts MessageCreateWithConfidentialTalent to TalentCardModel for template rendering
     * 
     * @param messageCreateDTO the input DTO containing talent information
     * @return TalentCardModel with formatted data for template rendering
     */
    public static TalentCardModel convertToTalentCardModel(MessageCreateWithConfidentialTalent messageCreateDTO) {
        if (messageCreateDTO == null) {
            log.warn("MessageCreateWithConfidentialTalent is null, returning empty model");
            return createEmptyModel();
        }

        try {
            return TalentCardModel.builder()
                    .fullName(formatFullName(messageCreateDTO.getFullName()))
                    .talentId(formatTalentId(messageCreateDTO.getTalentId()))
                    .avatarUrl(formatAvatarUrl(messageCreateDTO.getPhotoUrl()))
                    .topEducation(extractTopEducation(messageCreateDTO.getEducations()))
                    .currentLocation(formatLocation(messageCreateDTO.getCurrentLocation()))
                    .currentSalary(formatSalary(messageCreateDTO.getSalaryRange(), 
                                               messageCreateDTO.getCurrency(), 
                                               messageCreateDTO.getPayType()))
                    .experiences(convertExperiences(messageCreateDTO.getExperiences()))
                    .educations(convertEducations(messageCreateDTO.getEducations()))
                    .build();
        } catch (Exception e) {
            log.error("Error converting MessageCreateWithConfidentialTalent to TalentCardModel", e);
            return createEmptyModel();
        }
    }

    /**
     * Creates an empty model with default values
     */
    private static TalentCardModel createEmptyModel() {
        return TalentCardModel.builder()
                .fullName("Unknown")
                .talentId("")
                .avatarUrl("")
                .topEducation("")
                .currentLocation("")
                .currentSalary("")
                .experiences(Collections.emptyList())
                .educations(Collections.emptyList())
                .build();
    }

    /**
     * Formats the full name with null safety
     */
    private static String formatFullName(String fullName) {
        if (fullName == null || fullName.trim().isEmpty()) {
            return "Unknown";
        }
        return fullName.trim();
    }

    /**
     * Formats the talent ID with # prefix
     */
    private static String formatTalentId(Long talentId) {
        if (talentId == null) {
            return "";
        }
        return "#" + talentId;
    }

    /**
     * Formats the avatar URL with null safety
     */
    private static String formatAvatarUrl(String photoUrl) {
        if (photoUrl == null || photoUrl.trim().isEmpty()) {
            return "";
        }
        return photoUrl.trim();
    }

    /**
     * Extracts the highest education level from the education list
     */
    private static String extractTopEducation(List<TalentEducationDTO> educations) {
        if (educations == null || educations.isEmpty()) {
            return "";
        }

        // Find the highest degree level
        // Priority: 博士 > 硕士 > 本科 > 专科 > 其他
        for (TalentEducationDTO education : educations) {
            String degreeName = education.getDegreeName();
            if (degreeName != null && !degreeName.trim().isEmpty()) {
                String degree = degreeName.trim();
                if (degree.contains("博士") || degree.contains("PhD") || degree.contains("Doctor")) {
                    return "博士";
                }
            }
        }

        for (TalentEducationDTO education : educations) {
            String degreeName = education.getDegreeName();
            if (degreeName != null && !degreeName.trim().isEmpty()) {
                String degree = degreeName.trim();
                if (degree.contains("硕士") || degree.contains("Master") || degree.contains("研究生")) {
                    return "硕士";
                }
            }
        }

        for (TalentEducationDTO education : educations) {
            String degreeName = education.getDegreeName();
            if (degreeName != null && !degreeName.trim().isEmpty()) {
                String degree = degreeName.trim();
                if (degree.contains("本科") || degree.contains("Bachelor") || degree.contains("学士")) {
                    return "本科";
                }
            }
        }

        for (TalentEducationDTO education : educations) {
            String degreeName = education.getDegreeName();
            if (degreeName != null && !degreeName.trim().isEmpty()) {
                String degree = degreeName.trim();
                if (degree.contains("专科") || degree.contains("Associate") || degree.contains("大专")) {
                    return "专科";
                }
            }
        }

        // Return the first non-empty degree name if no standard degree found
        for (TalentEducationDTO education : educations) {
            String degreeName = education.getDegreeName();
            if (degreeName != null && !degreeName.trim().isEmpty()) {
                return degreeName.trim();
            }
        }

        return "";
    }

    /**
     * Formats the location information
     */
    private static String formatLocation(LocationDTO location) {
        if (location == null) {
            return "";
        }

        // Priority: city > province > country
        if (location.getCity() != null && !location.getCity().trim().isEmpty()) {
            return location.getCity().trim();
        }

        if (location.getProvince() != null && !location.getProvince().trim().isEmpty()) {
            return location.getProvince().trim();
        }

        if (location.getCountry() != null && !location.getCountry().trim().isEmpty()) {
            return location.getCountry().trim();
        }

        if (location.getLocation() != null && !location.getLocation().trim().isEmpty()) {
            return location.getLocation().trim();
        }

        return "";
    }

    /**
     * Formats the salary information with currency and pay type
     */
    private static String formatSalary(RangeDTO salaryRange, String currency, RateUnitType payType) {
        if (salaryRange == null) {
            return "";
        }

        BigDecimal salary = null;
        
        // Use the greater value if both gte and lte are present
        if (salaryRange.getGte() != null && salaryRange.getLte() != null) {
            salary = salaryRange.getGte().max(salaryRange.getLte());
        } else if (salaryRange.getGte() != null) {
            salary = salaryRange.getGte();
        } else if (salaryRange.getLte() != null) {
            salary = salaryRange.getLte();
        }

        if (salary == null || salary.compareTo(BigDecimal.ZERO) <= 0) {
            return "";
        }

        // Format the salary number
        String formattedSalary = SALARY_FORMAT.format(salary);

        // Add currency
        String currencyStr = (currency != null && !currency.trim().isEmpty()) ? currency.trim() : "RMB";

        // Add pay type suffix
        String payTypeSuffix = formatPayTypeSuffix(payType);

        return formattedSalary + currencyStr + payTypeSuffix;
    }

    /**
     * Formats the pay type suffix for salary display
     */
    private static String formatPayTypeSuffix(RateUnitType payType) {
        if (payType == null) {
            return "/年";
        }

        switch (payType) {
            case HOURLY:
                return "/小时";
            case DAILY:
                return "/天";
            case WEEKLY:
                return "/周";
            case MONTHLY:
                return "/月";
            case YEARLY:
                return "/年";
            default:
                return "/年";
        }
    }

    /**
     * Converts list of TalentExperienceDTO to list of ExperienceModel
     */
    private static List<ExperienceModel> convertExperiences(List<TalentExperienceDTO> experiences) {
        if (experiences == null || experiences.isEmpty()) {
            return Collections.emptyList();
        }

        return experiences.stream()
                .filter(exp -> exp != null && hasValidExperienceData(exp))
                .map(TalentCardModelConverter::convertExperience)
                .filter(exp -> exp != null) // Filter out any conversion failures
                .collect(Collectors.toList());
    }

    /**
     * Checks if experience has valid data for display
     */
    private static boolean hasValidExperienceData(TalentExperienceDTO experience) {
        return (experience.getCompanyName() != null && !experience.getCompanyName().trim().isEmpty()) ||
               (experience.getTitle() != null && !experience.getTitle().trim().isEmpty());
    }

    /**
     * Converts a single TalentExperienceDTO to ExperienceModel
     */
    private static ExperienceModel convertExperience(TalentExperienceDTO experience) {
        try {
            // Validate date range first
            if (!TalentCardTimeFormatter.validateDateRange(experience.getStartDate(), experience.getEndDate())) {
                log.warn("Invalid date range for experience: start={}, end={}", 
                        experience.getStartDate(), experience.getEndDate());
                // Use fallback formatting for invalid dates
                String timeRange = TalentCardTimeFormatter.handleInvalidDates(
                        experience.getStartDate(), 
                        experience.getEndDate(), 
                        experience.getCurrent()
                );
                
                return ExperienceModel.builder()
                        .companyName(experience.getCompanyName())
                        .title(experience.getTitle())
                        .startDate(experience.getStartDate())
                        .endDate(experience.getEndDate())
                        .current(experience.getCurrent())
                        .timeRange(timeRange)
                        .duration("")
                        .build();
            }

            // Use enhanced time formatting utilities for better edge case handling
            String timeRange = TalentCardTimeFormatter.formatExperienceTimeRange(
                    experience.getStartDate(), 
                    experience.getEndDate(), 
                    experience.getCurrent()
            );
            
            String duration = TalentCardTimeFormatter.formatExperienceDuration(
                    experience.getStartDate(), 
                    experience.getEndDate(), 
                    experience.getCurrent()
            );

            return ExperienceModel.builder()
                    .companyName(experience.getCompanyName())
                    .title(experience.getTitle())
                    .startDate(experience.getStartDate())
                    .endDate(experience.getEndDate())
                    .current(experience.getCurrent())
                    .timeRange(timeRange)
                    .duration(duration)
                    .build();
        } catch (Exception e) {
            log.error("Error converting experience: {}", experience, e);
            return null; // Will be filtered out by the calling method
        }
    }

    /**
     * Converts list of TalentEducationDTO to list of EducationModel
     */
    private static List<EducationModel> convertEducations(List<TalentEducationDTO> educations) {
        if (educations == null || educations.isEmpty()) {
            return Collections.emptyList();
        }

        return educations.stream()
                .filter(edu -> edu != null && hasValidEducationData(edu))
                .map(TalentCardModelConverter::convertEducation)
                .filter(Objects::nonNull) // Filter out any conversion failures
                .collect(Collectors.toList());
    }

    /**
     * Checks if education has valid data for display
     */
    private static boolean hasValidEducationData(TalentEducationDTO education) {
        return (education.getCollegeName() != null && !education.getCollegeName().trim().isEmpty()) ||
               (education.getDegreeName() != null && !education.getDegreeName().trim().isEmpty());
    }

    /**
     * Converts a single TalentEducationDTO to EducationModel
     */
    private static EducationModel convertEducation(TalentEducationDTO education) {
        try {
            // Use enhanced time formatting utilities for better edge case handling
            String timeInfo = TalentCardTimeFormatter.formatBestEducationTimeInfo(
                    education.getStartDate(), 
                    education.getEndDate(), 
                    education.getCurrent()
            );

            return EducationModel.builder()
                    .collegeName(education.getCollegeName())
                    .degreeName(education.getDegreeName())
                    .majorName(education.getMajorName())
                    .startDate(education.getStartDate())
                    .endDate(education.getEndDate())
                    .current(education.getCurrent())
                    .timeInfo(timeInfo)
                    .build();
        } catch (Exception e) {
            log.error("Error converting education: {}", education, e);
            return null; // Will be filtered out by the calling method
        }
    }
}