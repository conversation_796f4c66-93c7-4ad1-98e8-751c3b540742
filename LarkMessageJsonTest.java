import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;

/**
 * 测试 Lark 消息 JSON 生成功能
 */
public class LarkMessageJsonTest {
    
    public static void main(String[] args) {
        System.out.println("=== 测试 Lark 消息 JSON 生成功能 ===\n");
        
        // 创建测试数据
        MessageCreateWithConfidentialTalent message = createTestMessage();
        
        // 测试中文版本
        testChineseJson(message);
        
        // 测试英文版本
        testEnglishJson(message);
        
        // 测试完整版本（包含中英文）
        testFullJson(message);
        
        System.out.println("测试完成！");
    }
    
    private static MessageCreateWithConfidentialTalent createTestMessage() {
        MessageCreateWithConfidentialTalent message = new MessageCreateWithConfidentialTalent();
        
        // 基本信息
        message.setTalentId(2503524L);
        message.setFullName("张三丰");
        message.setConfidentialOwnerName("Eddie Xu");
        message.setPhotoUrl("img_v2_e5d9761f-3b78-47f2-9fa6-f8438c46861h");
        
        // 地址信息
        LocationDTO location = new LocationDTO();
        location.setCity("北京");
        message.setCurrentLocation(location);
        
        // 薪资信息
        RangeDTO salaryRange = new RangeDTO();
        salaryRange.setGte(new BigDecimal("800000"));
        message.setSalaryRange(salaryRange);
        message.setCurrency("RMB");
        message.setPayType(RateUnitType.YEARLY);
        
        // 工作经历
        TalentExperienceDTO experience = new TalentExperienceDTO();
        experience.setCompanyName("菲亚特克莱斯勒亚太投资有限公司");
        experience.setTitle("亚太财务分析经理");
        experience.setStartDate(LocalDate.of(2019, 7, 1));
        experience.setCurrent(true);
        message.setExperiences(Arrays.asList(experience));
        
        // 教育经历
        TalentEducationDTO education = new TalentEducationDTO();
        education.setCollegeName("对外经济贸易学院");
        education.setDegreeName("本科");
        education.setMajorName("金融学");
        education.setEndDate(LocalDate.of(2010, 6, 30));
        message.setEducations(Arrays.asList(education));
        
        return message;
    }
    
    private static void testChineseJson(MessageCreateWithConfidentialTalent message) {
        System.out.println("=== 中文版本 JSON ===");
        JSONObject cnJson = message.generateCnLarkMessageJson();
        String cnJsonStr = JSONUtil.toJsonPrettyStr(cnJson);
        System.out.println(cnJsonStr);
        System.out.println();
        
        // 验证关键字段
        validateJsonStructure(cnJson, "中文版本");
    }
    
    private static void testEnglishJson(MessageCreateWithConfidentialTalent message) {
        System.out.println("=== 英文版本 JSON ===");
        JSONObject enJson = message.generateEnLarkMessageJson();
        String enJsonStr = JSONUtil.toJsonPrettyStr(enJson);
        System.out.println(enJsonStr);
        System.out.println();
        
        // 验证关键字段
        validateJsonStructure(enJson, "英文版本");
    }
    
    private static void testFullJson(MessageCreateWithConfidentialTalent message) {
        System.out.println("=== 完整版本 JSON（包含中英文）===");
        JSONObject fullJson = message.generateFullLarkMessageJson();
        String fullJsonStr = JSONUtil.toJsonPrettyStr(fullJson);
        System.out.println(fullJsonStr);
        System.out.println();
        
        // 验证关键字段
        validateFullJsonStructure(fullJson);
    }
    
    private static void validateJsonStructure(JSONObject json, String version) {
        System.out.println("验证 " + version + " JSON 结构：");
        
        // 验证顶级结构
        checkField(json, "config", version + " - config");
        checkField(json, "header", version + " - header");
        checkField(json, "i18n_elements", version + " - i18n_elements");
        
        // 验证 config 结构
        JSONObject config = json.getJSONObject("config");
        checkField(config, "wide_screen_mode", version + " - config.wide_screen_mode");
        checkField(config, "enable_forward", version + " - config.enable_forward");
        checkField(config, "update_multi", version + " - config.update_multi");
        
        // 验证 header 结构
        JSONObject header = json.getJSONObject("header");
        checkField(header, "title", version + " - header.title");
        checkField(header, "template", version + " - header.template");
        
        System.out.println(version + " JSON 结构验证完成！\n");
    }
    
    private static void validateFullJsonStructure(JSONObject json) {
        System.out.println("验证完整版本 JSON 结构：");
        
        validateJsonStructure(json, "完整版本");
        
        // 验证中英文元素都存在
        JSONObject i18nElements = json.getJSONObject("i18n_elements");
        checkField(i18nElements, "zh_cn", "完整版本 - 中文元素");
        checkField(i18nElements, "en_us", "完整版本 - 英文元素");
        
        System.out.println("完整版本 JSON 结构验证完成！\n");
    }
    
    private static void checkField(JSONObject json, String field, String description) {
        boolean exists = json.containsKey(field) && json.get(field) != null;
        System.out.println("  " + description + ": " + (exists ? "✓ 存在" : "✗ 缺失"));
    }
}

// 模拟的 DTO 类（用于演示）
class LocationDTO {
    private String city;
    public String getCity() { return city; }
    public void setCity(String city) { this.city = city; }
}

class RangeDTO {
    private BigDecimal gte;
    public BigDecimal getGte() { return gte; }
    public void setGte(BigDecimal gte) { this.gte = gte; }
}

class TalentExperienceDTO {
    private String companyName;
    private String title;
    private LocalDate startDate;
    private Boolean current;
    
    public String getCompanyName() { return companyName; }
    public void setCompanyName(String companyName) { this.companyName = companyName; }
    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }
    public LocalDate getStartDate() { return startDate; }
    public void setStartDate(LocalDate startDate) { this.startDate = startDate; }
    public Boolean getCurrent() { return current; }
    public void setCurrent(Boolean current) { this.current = current; }
    public LocalDate getEndDate() { return null; }
}

class TalentEducationDTO {
    private String collegeName;
    private String degreeName;
    private String majorName;
    private LocalDate endDate;
    
    public String getCollegeName() { return collegeName; }
    public void setCollegeName(String collegeName) { this.collegeName = collegeName; }
    public String getDegreeName() { return degreeName; }
    public void setDegreeName(String degreeName) { this.degreeName = degreeName; }
    public String getMajorName() { return majorName; }
    public void setMajorName(String majorName) { this.majorName = majorName; }
    public LocalDate getEndDate() { return endDate; }
    public void setEndDate(LocalDate endDate) { this.endDate = endDate; }
    public LocalDate getStartDate() { return null; }
    public Boolean getCurrent() { return null; }
}

enum RateUnitType {
    YEARLY, MONTHLY, WEEKLY, DAILY, HOURLY
}
