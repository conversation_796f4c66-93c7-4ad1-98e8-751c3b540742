package com.altomni.apn.common.dto.message;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.altomni.apn.common.domain.dict.EnumCurrency;
import com.altomni.apn.common.domain.dict.EnumDegree;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.dto.RangeDTO;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.dto.talent.TalentDTOV3;
import com.altomni.apn.common.dto.talent.TalentEducationDTO;
import com.altomni.apn.common.dto.talent.TalentExperienceDTO;
import com.altomni.apn.common.service.enums.EnumCurrencyService;
import com.altomni.apn.common.service.enums.EnumDegreeService;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
@Setter
@NoArgsConstructor
@Slf4j
public class MessageCreateWithConfidentialTalent {

    private static final DecimalFormat SALARY_FORMAT = new DecimalFormat("#,###");
    private static final DateTimeFormatter YEAR_MONTH_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM");

    public static final String CN_LARK_TITLE = "保密候选人通知";
    public static final String EN_LARK_TITLE = "Confidential Candidates Notification";

    private Long tenantId;

    private Long needRemindUserId;
    /**
     * 保密操作的用户 id
     */
    private Long confidentialOwnerId;

    /**
     * 保密操作的用户名
     */
    private String confidentialOwnerName;

    /**
     * 保密的候选人 id
     */
    private Long talentId;

    /**
     * 候选人名称
     */
    private String fullName;

    /**
     * 候选人头像
     */
    private String photoUrl;

    /**
     * 候选人地址
     */
    private LocationDTO currentLocation;

    /**
     * 薪资币种
     */
    private String currency;

    /**
     * 薪资范围
     */
    private RangeDTO salaryRange;

    /**
     * 薪资方式
     */
    private RateUnitType payType;

    /**
     * 教育经历
     */
    private List<TalentEducationDTO> educations;

    /**
     * 工作经历
     */
    private List<TalentExperienceDTO> experiences;


    private EnumCurrencyService enumCurrencyService;
    private EnumDegreeService enumDegreeService;
    private String baseUrl;
    private Function<String, String> uploadImgGetKey;

    public void fillExtraInfo(EnumCurrencyService enumCurrencyService,
                              EnumDegreeService enumDegreeService,
                              String baseUrl,
                              Function<String, String> uploadImgGetKey) {
        this.enumCurrencyService = enumCurrencyService;
        this.enumDegreeService = enumDegreeService;
        this.baseUrl = baseUrl;
        this.uploadImgGetKey = uploadImgGetKey;
    }

    public MessageCreateWithConfidentialTalent(TalentDTOV3 talentInfo) {
        this.talentId = talentInfo.getId();
        this.tenantId = talentInfo.getTenantId();
        this.fullName = talentInfo.getFullName();
        this.photoUrl = talentInfo.getPhotoUrl();
        this.currentLocation = talentInfo.getCurrentLocation();
        this.currency = talentInfo.getCurrency();
        this.salaryRange = talentInfo.getSalaryRange();
        this.payType = talentInfo.getPayType();
        this.educations = talentInfo.getEducations();
        this.experiences = talentInfo.getExperiences();
    }

    public Optional<TalentExperienceDTO> nearExperience() {
        if (experiences == null) {
            return Optional.empty();
        }
        List<TalentExperienceDTO> nearExperience = experiences.stream().limit(1).toList();
        return nearExperience.stream().findFirst();
    }

    /**
     * {
     * "wide_screen_mode": true,
     * "enable_forward": true,
     * "update_multi": false
     * }
     */
    private static JSONObject larkMsgConfig() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("wide_screen_mode", true);
        jsonObject.put("enable_forward", true);
        jsonObject.put("update_multi", false);
        return jsonObject;
    }

    /**
     * {
     * "title": {
     * "tag": "plain_text",
     * "i18n": {
     * "en_us": "Confidential Candidates Notification",
     * "zh_cn": "保密候选人通知"
     * }
     * },
     * "template": "orange"
     * }
     */
    private static JSONObject larkMsgHeader() {
        JSONObject jsonObject = new JSONObject();
        JSONObject i18n = new JSONObject();
        i18n.put("en_us", EN_LARK_TITLE);
        i18n.put("zh_cn", CN_LARK_TITLE);
        JSONObject title = new JSONObject();
        title.put("tag", "plain_text");
        title.put("i18n", i18n);
        jsonObject.put("title", title);
        jsonObject.put("template", "orange");
        return jsonObject;
    }

    /**
     * {
     * "tag": "div",
     * "text": {
     * "tag": "lark_md",
     * "content": "The candidate [Zhang Sanfeng](https://your-link.com) #2503524 (Company: Fiat, Job Title: APAC Financial Analyst Manager) was set confidential by Eddie Xu"
     * }
     * }
     */
    private JSONObject larkMsgElementContent(boolean isCn) {
        JSONObject text = new JSONObject();
        text.put("tag", "lark_md");
        text.put("content", generateMainDescription(isCn));

        JSONObject element = new JSONObject();
        element.put("tag", "div");
        element.put("text", text);
        return element;
    }

    /**
     * {
     * "tag": "column_set",
     * "flex_mode": "none",
     * "columns": [
     * {
     * "tag": "column",
     * "width": "auto",
     * "vertical_align": "center",
     * "elements": [
     * {
     * "tag": "img",
     * "img_key": "img_v2_e5d9761f-3b78-47f2-9fa6-f8438c46861h",
     * "alt": {
     * "tag": "plain_text",
     * "content": "Avatar"
     * },
     * "mode": "crop_center",
     * "compact_width": false,
     * "custom_width": 150
     * }
     * ]
     * },
     * {
     * "tag": "column",
     * "width": "weighted",
     * "weight": 1,
     * "vertical_align": "center",
     * "elements": [
     * {
     * "tag": "div",
     * "text": {
     * "tag": "lark_md",
     * "content": "Zhang Sanfeng#2503524\nBachelor | Beijing | 800,000RMB/Year"
     * }
     * }
     * ]
     * }
     * ]
     * }
     */
    private JSONObject larkMsgElementBaseInfo(boolean isCn) {
        JSONObject element = new JSONObject();
        element.put("tag", "column_set");
        element.put("flex_mode", "none");
        JSONArray columns = new JSONArray();
        String imgKey = uploadImgGetKey.apply(photoUrl);
        if (imgKey != null) {
            JSONObject column = new JSONObject();
            column.put("tag", "column");
            column.put("width", "auto");
            column.put("vertical_align", "center");
            JSONObject img = new JSONObject();
            img.put("tag", "img");
            img.put("img_key", imgKey);
            JSONObject alt = new JSONObject();
            alt.put("tag", "plain_text");
            alt.put("content", "Avatar");
            img.put("alt", alt);
            img.put("mode", "fit_horizontal");
            img.put("compact_width", false);
            img.put("custom_width", 100);
            JSONArray columnElements = new JSONArray();
            columnElements.add(img);
            column.put("elements", columnElements);
            columns.add(column);
        }

        JSONObject column = new JSONObject();
        column.put("tag", "column");
        column.put("width", "weighted");
        column.put("weight", 1);
        column.put("vertical_align", "center");
        JSONArray columnElements = new JSONArray();
        JSONObject infoText = new JSONObject();
        infoText.put("tag", "div");
        JSONObject text = new JSONObject();
        text.put("tag", "lark_md");
        text.put("content", generateBasicInfo(isCn));
        infoText.put("text", text);
        columnElements.add(infoText);
        column.put("elements", columnElements);
        columns.add(column);

        element.put("columns", columns);
        return element;
    }

    /**
     * 生成工作经历元素
     * {
     * "tag": "column_set",
     * "flex_mode": "none",
     * "columns": [
     * {
     * "tag": "column",
     * "width": "weighted",
     * "weight": 3,
     * "elements": [
     * {
     * "tag": "div",
     * "text": {
     * "tag": "lark_md",
     * "content": "💼 菲亚特克莱斯勒亚太投资有限公司 · 亚太财务分析经理"
     * }
     * }
     * ]
     * },
     * {
     * "tag": "column",
     * "width": "weighted",
     * "weight": 1,
     * "elements": [
     * {
     * "tag": "div",
     * "text": {
     * "tag": "lark_md",
     * "content": "2019-07 - 至今 (6年)"
     * }
     * }
     * ]
     * }
     * ]
     * }
     */
    private JSONArray larkMsgElementExperiences(boolean isCn) {
        JSONArray experienceElements = new JSONArray();

        if (experiences == null || experiences.isEmpty()) {
            return experienceElements;
        }

        List<TalentExperienceDTO> nearTwoExperience = experiences.stream().limit(2).toList();
        for (TalentExperienceDTO exp : nearTwoExperience) {
            if (!hasValidExperienceData(exp)) {
                continue;
            }

            JSONObject experienceElement = new JSONObject();
            experienceElement.put("tag", "column_set");
            experienceElement.put("flex_mode", "none");

            JSONArray columns = new JSONArray();

            // 左列：公司和职位信息
            JSONObject leftColumn = new JSONObject();
            leftColumn.put("tag", "column");
            leftColumn.put("width", "weighted");
            leftColumn.put("weight", 3);

            JSONArray leftElements = new JSONArray();
            JSONObject companyDiv = new JSONObject();
            companyDiv.put("tag", "div");

            JSONObject companyText = new JSONObject();
            companyText.put("tag", "lark_md");

            String companyName = exp.getCompanyName() != null ? exp.getCompanyName().trim() : "";
            String title = exp.getTitle() != null ? exp.getTitle().trim() : "";
            String companyContent = "💼 ";
            if (!companyName.isEmpty()) {
                companyContent += companyName;
                if (!title.isEmpty()) {
                    companyContent += " · " + title;
                }
            } else if (!title.isEmpty()) {
                companyContent += title;
            }

            companyText.put("content", companyContent);
            companyDiv.put("text", companyText);
            leftElements.add(companyDiv);
            leftColumn.put("elements", leftElements);
            columns.add(leftColumn);

            // 右列：时间信息
            JSONObject rightColumn = new JSONObject();
            rightColumn.put("tag", "column");
            rightColumn.put("width", "weighted");
            rightColumn.put("weight", 1);

            JSONArray rightElements = new JSONArray();
            JSONObject timeDiv = new JSONObject();
            timeDiv.put("tag", "div");

            JSONObject timeText = new JSONObject();
            timeText.put("tag", "lark_md");

            String timeRange = formatExperienceTimeRange(exp.getStartDate(), exp.getEndDate(), exp.getCurrent(), isCn);
            String duration = formatExperienceDuration(exp.getStartDate(), exp.getEndDate(), exp.getCurrent(), isCn);

            String timeContent = timeRange;
            if (!duration.isEmpty()) {
                timeContent += " " + duration;
            }

            timeText.put("content", timeContent);
            timeDiv.put("text", timeText);
            rightElements.add(timeDiv);
            rightColumn.put("elements", rightElements);
            columns.add(rightColumn);

            experienceElement.put("columns", columns);
            experienceElements.add(experienceElement);
        }

        return experienceElements;
    }

    /**
     * 生成教育经历元素
     * {
     * "tag": "column_set",
     * "flex_mode": "none",
     * "columns": [
     * {
     * "tag": "column",
     * "width": "weighted",
     * "weight": 3,
     * "elements": [
     * {
     * "tag": "div",
     * "text": {
     * "tag": "lark_md",
     * "content": "🎓 对外经济贸易学院 · 本科 · 金融学"
     * }
     * }
     * ]
     * },
     * {
     * "tag": "column",
     * "width": "weighted",
     * "weight": 1,
     * "elements": [
     * {
     * "tag": "div",
     * "text": {
     * "tag": "lark_md",
     * "content": "2010 毕业"
     * }
     * }
     * ]
     * }
     * ]
     * }
     */
    private JSONArray larkMsgElementEducations(boolean isCn) {
        JSONArray educationElements = new JSONArray();

        if (educations == null || educations.isEmpty()) {
            return educationElements;
        }

        List<TalentEducationDTO> nearTwoEducations = educations.stream().limit(2).toList();
        for (TalentEducationDTO edu : nearTwoEducations) {
            if (!hasValidEducationData(edu)) {
                continue;
            }

            JSONObject educationElement = new JSONObject();
            educationElement.put("tag", "column_set");
            educationElement.put("flex_mode", "none");

            JSONArray columns = new JSONArray();

            // 左列：学校、学历、专业信息
            JSONObject leftColumn = new JSONObject();
            leftColumn.put("tag", "column");
            leftColumn.put("width", "weighted");
            leftColumn.put("weight", 3);

            JSONArray leftElements = new JSONArray();
            JSONObject schoolDiv = new JSONObject();
            schoolDiv.put("tag", "div");

            JSONObject schoolText = new JSONObject();
            schoolText.put("tag", "lark_md");

            String collegeName = edu.getCollegeName() != null ? edu.getCollegeName().trim() : "";
            String degreeName = edu.getDegreeLevel() != null ? edu.getDegreeLevel().trim() : "";
            String majorName = edu.getMajorName() != null ? edu.getMajorName().trim() : "";

            // 使用统一的学历转换方法
            if (!degreeName.isEmpty()) {
                degreeName = convertDegreeDisplay(degreeName, isCn);
            }

            String schoolContent = "🎓 ";
            if (!collegeName.isEmpty()) {
                schoolContent += collegeName;
                if (!degreeName.isEmpty()) {
                    schoolContent += " · " + degreeName;
                }
                if (!majorName.isEmpty()) {
                    schoolContent += " · " + majorName;
                }
            } else if (!degreeName.isEmpty()) {
                schoolContent += degreeName;
                if (!majorName.isEmpty()) {
                    schoolContent += " · " + majorName;
                }
            }

            schoolText.put("content", schoolContent);
            schoolDiv.put("text", schoolText);
            leftElements.add(schoolDiv);
            leftColumn.put("elements", leftElements);
            columns.add(leftColumn);

            // 右列：时间信息
            JSONObject rightColumn = new JSONObject();
            rightColumn.put("tag", "column");
            rightColumn.put("width", "weighted");
            rightColumn.put("weight", 1);

            JSONArray rightElements = new JSONArray();
            JSONObject timeDiv = new JSONObject();
            timeDiv.put("tag", "div");

            JSONObject timeText = new JSONObject();
            timeText.put("tag", "lark_md");

            String timeInfo = formatBestEducationTimeInfo(edu.getStartDate(), edu.getEndDate(), edu.getCurrent(), isCn);

            timeText.put("content", timeInfo);
            timeDiv.put("text", timeText);
            rightElements.add(timeDiv);
            rightColumn.put("elements", rightElements);
            columns.add(rightColumn);

            educationElement.put("columns", columns);
            educationElements.add(educationElement);
        }

        return educationElements;
    }

    /**
     * 转换学历显示名称
     *
     * @param degreeName 学历名称
     * @param isCn       是否为中文版本
     */
    private String convertDegreeDisplay(String degreeName, boolean isCn) {
        if (degreeName == null || degreeName.trim().isBlank()) {
            return "";
        }

        List<EnumDegree> enumDegrees = enumDegreeService.transferDegreesByNamesToList(List.of(degreeName));
        if (enumDegrees != null && !enumDegrees.isEmpty()) {
            EnumDegree degreeEnum = enumDegrees.get(0);
            return isCn ? degreeEnum.getCnDisplay() : degreeEnum.getEnDisplay();
        } else {
            return "";
        }
    }


    /**
     * 生成详情按钮元素
     * {
     * "tag": "action",
     * "actions": [
     * {
     * "tag": "button",
     * "text": {
     * "tag": "plain_text",
     * "content": "点击查看详情"
     * },
     * "type": "primary",
     * "url": "https://your-detail-page.com"
     * }
     * ]
     * }
     */
    private JSONObject larkMsgElementDetailButton(boolean isCn) {
        JSONObject actionElement = new JSONObject();
        actionElement.put("tag", "action");

        JSONArray actions = new JSONArray();
        JSONObject button = new JSONObject();
        button.put("tag", "button");
        button.put("type", "primary");

        JSONObject buttonText = new JSONObject();
        buttonText.put("tag", "plain_text");
        buttonText.put("content", isCn ? "点击查看详情" : "Click to check details");

        button.put("text", buttonText);
        button.put("url", generateDetailUrl());

        actions.add(button);
        actionElement.put("actions", actions);

        return actionElement;
    }

    /**
     * 生成完整的 Lark 消息 JSON
     *
     * @return 完整的 Lark 消息 JSON 对象
     */
    public JSONObject generateLarkMessageJson() {
        JSONObject message = new JSONObject();

        // 配置部分
        message.put("config", larkMsgConfig());

        // 头部部分
        message.put("header", larkMsgHeader());

        // 国际化元素部分
        JSONObject i18nElements = new JSONObject();
        i18nElements.put("zh_cn", generateLarkMessageI18nElements(true));
        i18nElements.put("en_us", generateLarkMessageI18nElements(false));

        message.put("i18n_elements", i18nElements);

        return message;
    }

    private JSONArray generateLarkMessageI18nElements(boolean isCn) {
        JSONArray elements = new JSONArray();

        // 主要描述
        elements.add(larkMsgElementContent(isCn));

        // 基本信息
        elements.add(larkMsgElementBaseInfo(isCn));

        // 工作经历
        JSONArray experienceElements = larkMsgElementExperiences(isCn);
        elements.addAll(experienceElements);

        // 教育经历
        JSONArray educationElements = larkMsgElementEducations(isCn);
        elements.addAll(educationElements);

        // 详情按钮
        elements.add(larkMsgElementDetailButton(isCn));
        return elements;
    }


    /**
     * 生成主要描述文本
     *
     * @param isCn 是否为中文版本
     */
    private String generateMainDescription(boolean isCn) {
        String candidateName = formatFullName(fullName);
        String talentIdStr = formatTalentId(talentId);
        String companyInfo = "";
        String jobTitleInfo = "";

        String company = isCn ? "公司：" : " Company: ";
        String jobTitle = isCn ? "职位：" : " Job Title: ";
        String defaultOwner = isCn ? "系统管理员" : "System Administrator";
        String formatStr = isCn ? "候选人 [%s](%s) %s%s%s已被 %s 设为保密" : "The candidate [%s](%s) %s%s%s was set confidential by %s";

        Optional<TalentExperienceDTO> nearExp = nearExperience();
        if (nearExp.isPresent()) {
            TalentExperienceDTO exp = nearExp.get();
            if (exp.getCompanyName() != null && !exp.getCompanyName().trim().isEmpty()) {
                companyInfo = "(" + company + exp.getCompanyName().trim();
            }
            if (exp.getTitle() != null && !exp.getTitle().trim().isEmpty()) {
                jobTitleInfo = jobTitle + exp.getTitle().trim() + ")";
            }
        }

        String ownerName = (confidentialOwnerName != null && !confidentialOwnerName.trim().isEmpty())
                ? confidentialOwnerName.trim() : defaultOwner;

        return String.format(formatStr,
                candidateName, generateDetailUrl(), talentIdStr, companyInfo, jobTitleInfo, ownerName);
    }


    /**
     * 生成候选人基本信息
     *
     * @param isCn 是否为中文版本
     */
    private String generateBasicInfo(boolean isCn) {
        String name = formatFullName(fullName);
        String id = formatTalentId(talentId);
        String education = extractTopEducation(educations, isCn);
        String location = formatLocation(currentLocation);
        String salary = formatSalary(salaryRange, currency, payType, isCn);

        StringBuilder info = new StringBuilder();
        info.append(name).append(id);

        if (!education.isEmpty() || !location.isEmpty() || !salary.isEmpty()) {
            info.append("\n");
            boolean hasContent = false;

            if (!education.isEmpty()) {
                info.append(education);
                hasContent = true;
            }

            if (!location.isEmpty()) {
                if (hasContent) info.append(" | ");
                info.append(location);
                hasContent = true;
            }

            if (!salary.isEmpty()) {
                if (hasContent) info.append(" | ");
                info.append(salary);
            }
        }

        return info.toString();
    }


    /**
     * 格式化全名
     */
    private String formatFullName(String fullName) {
        if (fullName == null || fullName.trim().isEmpty()) {
            return "Unknown";
        }
        return fullName.trim();
    }

    /**
     * 格式化候选人ID
     */
    private String formatTalentId(Long talentId) {
        if (talentId == null) {
            return "";
        }
        return "#" + talentId;
    }

    /**
     * 生成详情页面URL
     */
    private String generateDetailUrl() {
        // 这里可以根据实际需要配置基础URL
        return baseUrl + "/candidates/detail/" + (talentId != null ? talentId : "");
    }

    /**
     * 提取最高学历
     *
     * @param educations 教育经历列表
     * @param isCn       是否为中文版本
     */
    private String extractTopEducation(List<TalentEducationDTO> educations, boolean isCn) {
        if (educations == null || educations.isEmpty()) {
            return "";
        }
        // 找到最高学历的教育经历
        List<String> degreeNames = educations.stream().map(TalentEducationDTO::getDegreeLevel).toList();
        List<EnumDegree> enumDegrees = enumDegreeService.transferDegreesByNamesToList(degreeNames);
        Optional<EnumDegree> maxDegreeOpt = enumDegrees.stream().max(Comparator.comparing(EnumDegree::getScore));
        return maxDegreeOpt.map(degree -> isCn ? degree.getCnDisplay() : degree.getEnDisplay()).orElse("");
    }

    /**
     * 格式化地址信息
     */
    private String formatLocation(LocationDTO location) {
        if (location == null) {
            return "";
        }

        // 优先级：city > province > country > location
        if (location.getCity() != null && !location.getCity().trim().isEmpty()) {
            return location.getCity().trim();
        }

        if (location.getProvince() != null && !location.getProvince().trim().isEmpty()) {
            return location.getProvince().trim();
        }

        if (location.getCountry() != null && !location.getCountry().trim().isEmpty()) {
            return location.getCountry().trim();
        }

        if (location.getLocation() != null && !location.getLocation().trim().isEmpty()) {
            return location.getLocation().trim();
        }

        return "";
    }

    /**
     * 格式化薪资信息
     *
     * @param salaryRange 薪资范围
     * @param currency    币种代码
     * @param payType     薪资类型
     * @param isCn        是否为中文版本
     */
    private String formatSalary(RangeDTO salaryRange, String currency, RateUnitType payType, boolean isCn) {
        if (salaryRange == null) {
            return "";
        }

        BigDecimal salary = null;

        // 使用较大的值
        if (salaryRange.getGte() != null && salaryRange.getLte() != null) {
            salary = salaryRange.getGte().max(salaryRange.getLte());
        } else if (salaryRange.getGte() != null) {
            salary = salaryRange.getGte();
        } else if (salaryRange.getLte() != null) {
            salary = salaryRange.getLte();
        }

        if (salary == null || salary.compareTo(BigDecimal.ZERO) <= 0) {
            return "";
        }

        // 格式化薪资数字
        String formattedSalary = SALARY_FORMAT.format(salary);

        // 使用 EnumCurrencyService 获取币种显示名称
        String currencyDisplay = getCurrencyDisplay(currency, isCn);

        // 添加薪资类型后缀
        String payTypeSuffix = formatPayTypeSuffix(payType, isCn);

        return formattedSalary + currencyDisplay + payTypeSuffix;
    }

    /**
     * 获取币种显示名称
     */
    private String getCurrencyDisplay(String currency, boolean isCn) {
        if (currency == null || currency.trim().isEmpty()) {
            return "RMB"; // 默认币种
        }

        List<EnumCurrency> allEnumCurrency = enumCurrencyService.findAllEnumCurrency();
        Map<String, EnumCurrency> currencyMap = allEnumCurrency.stream().collect(Collectors.toMap(EnumCurrency::getName, Function.identity()));
        if (currencyMap.containsKey(currency.trim())) {
            EnumCurrency currencyEnum = currencyMap.get(currency.trim());
            return isCn ? currencyEnum.getCnDisplay() : currencyEnum.getEnDisplay();
        } else {
            return currency;
        }
    }

    /**
     * 格式化薪资类型后缀
     *
     * @param payType 薪资类型
     * @param isCn    是否为中文版本
     */
    private String formatPayTypeSuffix(RateUnitType payType, boolean isCn) {
        if (payType == null) {
            return isCn ? "/年" : "/Year";
        }

        if (isCn) {
            return switch (payType) {
                case HOURLY -> "/小时";
                case DAILY -> "/天";
                case WEEKLY -> "/周";
                case MONTHLY -> "/月";
                default -> "/年";
            };
        } else {
            return switch (payType) {
                case HOURLY -> "/Hour";
                case DAILY -> "/Day";
                case WEEKLY -> "/Week";
                case MONTHLY -> "/Month";
                default -> "/Year";
            };
        }
    }


    /**
     * 格式化工作经历时间范围
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param current   是否为当前职位
     * @param isCn      是否为中文版本
     */
    private String formatExperienceTimeRange(LocalDate startDate, LocalDate endDate, Boolean current, boolean isCn) {
        if (startDate == null) {
            return "";
        }

        try {
            String startStr = startDate.format(YEAR_MONTH_FORMATTER);
            String currentText = isCn ? "至今" : "Present";

            // 处理当前职位
            if (current != null && current) {
                return startStr + " - " + currentText;
            }

            // 处理结束日期
            if (endDate != null) {
                String endStr = endDate.format(YEAR_MONTH_FORMATTER);
                return startStr + " - " + endStr;
            }

            // 默认为当前职位
            return startStr + " - " + currentText;
        } catch (Exception e) {
            log.error("Error formatting experience time range", e);
            return "";
        }
    }


    /**
     * 格式化工作经历持续时间
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param current   是否为当前职位
     * @param isCn      是否为中文版本
     */
    private String formatExperienceDuration(LocalDate startDate, LocalDate endDate, Boolean current, boolean isCn) {
        if (startDate == null) {
            return "";
        }
        try {
            LocalDate actualEndDate = determineActualEndDate(endDate, current);
            return calculateAndFormatDuration(startDate, actualEndDate, isCn);
        } catch (Exception e) {
            log.error("Error formatting experience duration", e);
            return "";
        }
    }

    /**
     * 格式化教育时间信息
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param current   是否在读
     * @param isCn      是否为中文版本
     */
    private String formatBestEducationTimeInfo(LocalDate startDate, LocalDate endDate, Boolean current, boolean isCn) {
        try {
            // 最高优先级：在读状态
            if (current != null && current) {
                return isCn ? " 在读" : "Currently studying";
            }

            // 第二优先级：毕业年份
            if (endDate != null) {
                return isCn ? endDate.getYear() + " 毕业" : "Graduated in " + endDate.getYear();
            }

            // 第三优先级：入学年份
            if (startDate != null) {
                return isCn ? startDate.getYear() + " 入学" : "Enrolled in " + startDate.getYear();
            }

            return "";
        } catch (Exception e) {
            log.error("Error formatting best education time info", e);
            return "";
        }
    }

    /**
     * 确定实际结束日期
     */
    private LocalDate determineActualEndDate(LocalDate endDate, Boolean current) {
        // 如果没有结束日期且不是明确的非当前职位，假设为当前职位
        if (current != null && current) {
            return LocalDate.now();
        } else {
            return Objects.requireNonNullElseGet(endDate, LocalDate::now);
        }
    }

    /**
     * 计算并格式化持续时间
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param isCn      是否为中文版本
     */
    private String calculateAndFormatDuration(LocalDate startDate, LocalDate endDate, boolean isCn) {
        long totalMonths = ChronoUnit.MONTHS.between(startDate, endDate);

        // 处理负持续时间（无效日期）
        if (totalMonths < 0) {
            log.warn("Negative duration calculated between {} and {}", startDate, endDate);
            totalMonths = 0;
        }

        long years = totalMonths / 12;
        long months = totalMonths % 12;

        // 格式化持续时间字符串
        if (isCn) {
            if (years == 0 && months == 0) {
                return "(不足1个月)";
            } else if (years == 0) {
                return "(" + months + "个月)";
            } else if (months == 0) {
                return "(" + years + "年)";
            } else {
                return "(" + years + "年" + months + "个月)";
            }
        } else {
            if (years == 0 && months == 0) {
                return "(Less than 1 month)";
            } else if (years == 0) {
                return "(" + months + (months == 1 ? " month)" : " months)");
            } else if (months == 0) {
                return "(" + years + (years == 1 ? " year)" : " years)");
            } else {
                return "(" + years + (years == 1 ? " year " : " years ") +
                        months + (months == 1 ? " month)" : " months)");
            }
        }
    }

    /**
     * 检查工作经历是否有有效数据
     */
    private boolean hasValidExperienceData(TalentExperienceDTO experience) {
        return experience != null &&
                ((experience.getCompanyName() != null && !experience.getCompanyName().trim().isEmpty()) ||
                        (experience.getTitle() != null && !experience.getTitle().trim().isEmpty()));
    }

    /**
     * 检查教育经历是否有有效数据
     */
    private boolean hasValidEducationData(TalentEducationDTO education) {
        return education != null &&
                ((education.getCollegeName() != null && !education.getCollegeName().trim().isEmpty()) ||
                        (education.getDegreeLevel() != null && !education.getDegreeLevel().trim().isEmpty()));
    }

}
