import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;

/**
 * 演示 cnLarkContent() 和 enLarkContent() 方法的实际返回内容
 */
public class ContentDemo {
    
    public static void main(String[] args) {
        // 创建测试数据
        MessageCreateWithConfidentialTalent message = new MessageCreateWithConfidentialTalent();
        
        // 设置基本信息
        message.setTalentId(2503524L);
        message.setFullName("张三丰");
        message.setConfidentialOwnerName("Eddie Xu");
        
        // 设置地址信息
        LocationDTO location = new LocationDTO();
        location.setCity("北京");
        message.setCurrentLocation(location);
        
        // 设置薪资信息
        RangeDTO salaryRange = new RangeDTO();
        salaryRange.setGte(new BigDecimal("800000"));
        message.setSalaryRange(salaryRange);
        message.setCurrency("RMB");
        message.setPayType(RateUnitType.YEARLY);
        
        // 设置工作经历
        TalentExperienceDTO experience = new TalentExperienceDTO();
        experience.setCompanyName("菲亚特克莱斯勒亚太投资有限公司");
        experience.setTitle("亚太财务分析经理");
        experience.setStartDate(LocalDate.of(2019, 7, 1));
        experience.setCurrent(true);
        message.setExperiences(Arrays.asList(experience));
        
        // 设置教育经历
        TalentEducationDTO education = new TalentEducationDTO();
        education.setCollegeName("对外经济贸易学院");
        education.setDegreeName("本科");
        education.setMajorName("金融学");
        education.setEndDate(LocalDate.of(2010, 6, 30));
        message.setEducations(Arrays.asList(education));
        
        // 显示中文内容
        System.out.println("=== cnLarkContent() 返回的内容 ===");
        String cnContent = message.cnLarkContent();
        System.out.println(cnContent);
        System.out.println();
        
        // 显示英文内容
        System.out.println("=== enLarkContent() 返回的内容 ===");
        String enContent = message.enLarkContent();
        System.out.println(enContent);
        System.out.println();
        
        // 显示内容结构分析
        System.out.println("=== 内容结构分析 ===");
        System.out.println("中文内容行数: " + cnContent.split("\n").length);
        System.out.println("英文内容行数: " + enContent.split("\n").length);
        System.out.println("中文内容字符数: " + cnContent.length());
        System.out.println("英文内容字符数: " + enContent.length());
    }
}

// 模拟的 DTO 类（用于演示）
class LocationDTO {
    private String city;
    public String getCity() { return city; }
    public void setCity(String city) { this.city = city; }
}

class RangeDTO {
    private BigDecimal gte;
    public BigDecimal getGte() { return gte; }
    public void setGte(BigDecimal gte) { this.gte = gte; }
}

class TalentExperienceDTO {
    private String companyName;
    private String title;
    private LocalDate startDate;
    private Boolean current;
    
    public String getCompanyName() { return companyName; }
    public void setCompanyName(String companyName) { this.companyName = companyName; }
    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }
    public LocalDate getStartDate() { return startDate; }
    public void setStartDate(LocalDate startDate) { this.startDate = startDate; }
    public Boolean getCurrent() { return current; }
    public void setCurrent(Boolean current) { this.current = current; }
}

class TalentEducationDTO {
    private String collegeName;
    private String degreeName;
    private String majorName;
    private LocalDate endDate;
    
    public String getCollegeName() { return collegeName; }
    public void setCollegeName(String collegeName) { this.collegeName = collegeName; }
    public String getDegreeName() { return degreeName; }
    public void setDegreeName(String degreeName) { this.degreeName = degreeName; }
    public String getMajorName() { return majorName; }
    public void setMajorName(String majorName) { this.majorName = majorName; }
    public LocalDate getEndDate() { return endDate; }
    public void setEndDate(LocalDate endDate) { this.endDate = endDate; }
}

enum RateUnitType {
    YEARLY, MONTHLY, WEEKLY, DAILY, HOURLY
}
