# Lark 消息 JSON 生成功能使用说明

## 概述

`MessageCreateWithConfidentialTalent` 类现在提供了完整的 Lark 卡片消息 JSON 生成功能，包括：

1. **文本内容生成**：`cnLarkContent()` 和 `enLarkContent()` 方法
2. **完整 JSON 生成**：多个方法生成符合 Lark 卡片格式的 JSON

## 新增的 JSON 生成方法

### 1. 基础组件方法

- `larkMsgConfig()` - 生成配置部分
- `larkMsgHeader()` - 生成头部部分
- `larkMsgElementContent(boolean isCn)` - 生成主要描述元素
- `larkMsgElementBaseInfo(boolean isCn)` - 生成基本信息元素
- `larkMsgElementExperiences(boolean isCn)` - 生成工作经历元素数组
- `larkMsgElementEducations(boolean isCn)` - 生成教育经历元素数组
- `larkMsgElementDetailButton(boolean isCn)` - 生成详情按钮元素

### 2. 完整 JSON 生成方法

- `generateCnLarkMessageJson()` - 生成中文版本的完整 JSON
- `generateEnLarkMessageJson()` - 生成英文版本的完整 JSON
- `generateFullLarkMessageJson()` - 生成包含中英文的完整 JSON

## 使用方式

### 基本使用

```java
// 创建消息对象
MessageCreateWithConfidentialTalent message = new MessageCreateWithConfidentialTalent(talentInfo);
message.setConfidentialOwnerName("操作者姓名");

// 方式1：生成纯文本内容（原有功能）
String cnContent = message.cnLarkContent();
String enContent = message.enLarkContent();

// 方式2：生成完整的 Lark JSON（新功能）
JSONObject cnJson = message.generateCnLarkMessageJson();
JSONObject enJson = message.generateEnLarkMessageJson();
JSONObject fullJson = message.generateFullLarkMessageJson();
```

### 发送到 Lark

```java
// 生成完整的 Lark 消息 JSON
JSONObject larkMessage = message.generateFullLarkMessageJson();

// 转换为字符串
String jsonString = JSONUtil.toJsonStr(larkMessage);

// 发送到 Lark API
larkClient.sendCardMessage(userId, jsonString);
```

## JSON 结构说明

生成的 JSON 完全符合 `msg.json` 的格式，包含以下部分：

### 1. Config 配置
```json
{
  "config": {
    "wide_screen_mode": true,
    "enable_forward": true,
    "update_multi": false
  }
}
```

### 2. Header 头部
```json
{
  "header": {
    "title": {
      "tag": "plain_text",
      "i18n": {
        "en_us": "Confidential Candidates Notification",
        "zh_cn": "保密候选人通知"
      }
    },
    "template": "orange"
  }
}
```

### 3. I18n Elements 国际化元素
包含中文（zh_cn）和英文（en_us）两个版本的元素数组：

- **主要描述元素**：候选人被设为保密的通知
- **基本信息元素**：包含头像、姓名、学历、地址、薪资
- **工作经历元素**：每个工作经历一个元素，包含公司、职位、时间
- **教育经历元素**：每个教育经历一个元素，包含学校、学历、专业、时间
- **详情按钮元素**：跳转到候选人详情页面的按钮

## 特性说明

### 1. 国际化支持
- 自动处理中英文内容
- 学历名称自动转换（本科→Bachelor，硕士→Master等）
- 时间格式本地化（至今→Present，毕业→Graduated等）

### 2. 数据验证
- 自动过滤无效的工作经历和教育经历
- 处理空值和缺失字段
- 提供合理的默认值

### 3. 灵活的生成选项
- 可以生成单语言版本（中文或英文）
- 可以生成双语言版本
- 支持只生成文本内容或完整 JSON

### 4. 与原有功能兼容
- 保留了原有的 `cnLarkContent()` 和 `enLarkContent()` 方法
- 新增的 JSON 生成功能不影响现有代码

## 测试

运行 `LarkMessageJsonTest.java` 可以测试所有功能：

```bash
javac LarkMessageJsonTest.java
java LarkMessageJsonTest
```

测试将验证：
- JSON 结构的完整性
- 中英文内容的正确性
- 与 msg.json 格式的一致性

## 注意事项

1. **头像处理**：如果 `photoUrl` 为空，将不会在 JSON 中包含头像元素
2. **URL 配置**：详情按钮的 URL 通过 `generateDetailUrl()` 方法生成，可能需要根据实际环境配置
3. **时间计算**：工作经历的持续时间会根据当前时间动态计算
4. **数据完整性**：建议在调用前确保必要的字段（如 talentId、fullName）已设置

## 示例输出

生成的 JSON 将类似于 `msg.json` 的格式，包含完整的 Lark 卡片消息结构，可以直接用于 Lark API 调用。
